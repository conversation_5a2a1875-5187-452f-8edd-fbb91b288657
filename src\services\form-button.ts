import { ButtonOperateType } from '@/const/button-config';
import { ExecutionEventType, PostEntryType } from '@/pages/app-designer/components/right-panel/form-design/button-options/constants';

/**
 * com.ideal.gwy.datasource.model.request.form.FormUpdateRequest.FormButtonConfig
 *
 * FormButtonConfig
 */
export type FormButtonConfig = {
  /**
   * 按钮样式
   */
  buttonStyle?: null | string;
  /**
   * 执行事件
   */
  events?: FormButtonEvent[] | null;
  /**
   * 按钮名称
   */
  operateType?: ButtonOperateType;
  [property: string]: any;
};

/**
 * com.ideal.gwy.datasource.model.request.form.FormUpdateRequest.FormButtonEvent
 *
 * FormButtonEvent
 */
export type FormButtonEvent = {
  /**
   * 数据同步配置
   */
  dataSyncConfigs?: any[] | null;
  /**
   * 按钮事件类型
   */
  eventType?: ExecutionEventType;
  /**
   * 入离职配置
   */
  postEntryOrLevelConfig?: PostEntryOrLevelConfig;
  [property: string]: any;
};

export type FormTag = {
  /**
   * 数据单元id
   */
  dataUnitId?: number | null;
  /**
   * 标签id
   */
  tagId?: number | null;
  [property: string]: any;
};

/**
 * 邀请函类型
 */
export enum PostInviteType {
  Custom = 'CUSTOM',
  Default = 'DEFAULT',
}

/**
 * 入离职配置
 *
 * PostEntryOrLevelConfig
 */
export type PostEntryOrLevelConfig = {
  /**
   * 入职类型-多选
   */
  postEntryTypeList?: PostEntryType[] | null;
  /**
   * 邀请函类型
   */
  postInviteType?: PostInviteType;
  /**
   * 标签传参至附件,邀请函附件
   */
  toAttachment?: FormTag;
  /**
   * 标签传参至合同期限
   */
  toContractPeriod?: FormTag;
  /**
   * 标签传参至合同类型
   */
  toContractType?: FormTag;
  /**
   * 标签传参至入职时间
   */
  toEntryTime?: FormTag;
  /**
   * 标签传参至离职时间
   */
  toLevelTime?: FormTag;
  /**
   * 标签传参至岗位
   */
  toPost?: FormTag;
  /**
   * 标签传参至基本工资
   */
  toSalary?: FormTag;
  /**
   * 标签传参至试用期
   */
  toTrialPeriod?: FormTag;
  /**
   * 标签传参至用户
   */
  toUser?: FormTag;
  /**
   * 标签传参至共用类型
   */
  toWorkType?: FormTag;
  [property: string]: any;
};
