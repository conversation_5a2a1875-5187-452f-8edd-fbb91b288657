.container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  height: 90px;
  border: 1px dashed #c9cdd4;
  border-radius: 2px;
  margin-bottom: 10px;
  background-color: #fff;

  .idx {
    display: flex;
    align-items: center;
    justify-content: center;
    flex: 0 0 34px;
    height: 100%;
    background-color: #f7f8fa;
  }

  .content {
    width: 100%;
    padding: 0 10px;

    .contentItem {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      gap: 10px;

      :global {
        .ant-form-item {
          margin-bottom: 0;
        }
      }

      .settingBtn {
        display: inline-block;
        width: 160px;
        height: 30px;
        line-height: 30px;
        text-align: center;
        border: 1px solid #c9cdd4;
        color: #1d2129;
        background: #fff;
        cursor: pointer;
      }
    }
  }

  .right {
    display: flex;
    align-items: center;
    justify-content: center;
    flex: 0 0 36px;

    .delete {
      font-size: 16px;
      cursor: pointer;
    }
  }
}
