import { FormButtonConfig } from '@/services/form-button';
import { Form, Select } from 'antd';
import { useState } from 'react';

interface IProps {
  form: any;
  btnItem: FormButtonConfig;
}

const LevelPost = ({ form, btnItem }: IProps) => {
  const [postOptions, setPostOptions] = useState<any[]>([]);

  // const getPostOptions = async () => {
  //   const events = btnItem.events || [];
  //   const leaveEvent = events.find((item) => item.optType === ExecutionEventType.LEAVE_POST);
  //   if (!leaveEvent) return;
  //   const values = await form?.getFieldsValue();
  //   const postTag = leaveEvent.params?.[0];
  //   const userTag = leaveEvent.params?.[1];
  //   const postId =
  //     get(values, ['orgList', 0, `${postTag?.ruleId}-${postTag?.tagId}`, 'tagValues', 0, 'value', 'postId']) || '';
  //   const userId =
  //     get(values, ['orgList', 0, `${userTag?.ruleId}-${userTag?.tagId}`, 'tagValues', 0, 'value', 'userId']) || '';
  //   const postList = await org.getUserCommonPostList({ userId, postId });
  //   setPostOptions(
  //     (postList || []).map((item) => ({
  //       value: item.postId,
  //       label: `${item.orgShortName || item.orgName}-${item.departmentName}-${item.postName}`,
  //     })),
  //   );
  // };

  // useEffect(() => {
  //   getPostOptions();
  // }, [form]);

  return (
    <Form.Item name="levelPostIdList" label="选择岗位">
      <Select options={postOptions} mode="multiple" maxTagCount="responsive" placeholder="请选择" />
    </Form.Item>
  );
};

export default LevelPost;
