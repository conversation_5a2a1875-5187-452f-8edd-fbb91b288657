import IconFormApprove from '@/assets/app-form-manage/icon-form-approve.png';
import IconFormExcute from '@/assets/app-form-manage/icon-form-execution.png';
import { MetaAppStatus } from '@/const/metadata';
import { useBridge } from '@/hooks';
import { openAppDesigner } from '@/pages/app-designer';
import { formManageApi } from '@/services';
import {
  AuditOutlined,
  CaretRightOutlined,
  CheckCircleOutlined,
  DeleteOutlined,
  FileProtectOutlined,
  FolderOutlined,
  StopOutlined,
  VerticalAlignBottomOutlined,
  VerticalAlignTopOutlined,
} from '@ant-design/icons';
import {
  Breadcrumb,
  Button,
  Collapse,
  Divider,
  Dropdown,
  Empty,
  Input,
  MenuProps,
  message,
  Modal,
  Tag,
  TextEllipsisTooltip,
} from '@gwy/components-web';
import { cloneDeep, get, isEmpty } from 'lodash-es';
import { useMemo, useState } from 'react';
import AuthorizationManagement from '../authorization-management';
import BatchFormSetting from '../batch-form-setting';
import FolderMoveModal from '../folder-moved';
import SortList from '../sort-list';
import FolderCard from './components/folder-card';
import styled from './index.less';

export enum ActionType {
  ENABLE = 'true', // 启用
  DISABLE = 'false', // 禁用
  //   DELETE, // 删除
}
export const tagType = {
  [ActionType.ENABLE]: { color: 'green', backgroundColor: '#E8FFEA', border: '1px solid #00B42A', label: '启用' },
  [ActionType.DISABLE]: { color: 'default', backgroundColor: '#F2F3F5', border: '1px solid #86909C', label: '禁用' },
};

export const executeTagType = {
  [MetaAppStatus.Approved]: { color: 'green', backgroundColor: '#f6ffec', label: '同意' },
  [MetaAppStatus.Rejected]: { color: 'error', backgroundColor: '#FFECE8', label: '拒绝' },
  [MetaAppStatus.PendingApproval]: { color: 'warning', backgroundColor: '#F2F3F5', label: '审核中' },
  [MetaAppStatus.Stash]: { color: 'default', backgroundColor: '#F2F3F5', label: '暂存' },
};

interface Iprops {
  fetchFolderAppList: (postId: string) => void;
  baseInfo: any;
  folderAppList: any[];
  setCheckConfigModal: (modal: any) => void;
}

const CardItem = (props: Iprops) => {
  const bridge = useBridge();
  const { fetchFolderAppList, baseInfo, folderAppList, setCheckConfigModal } = props;
  const [dropVisable, setDropVisable] = useState<boolean>(false);
  const [clickFieldId, setClickFieldId] = useState(null);
  const [editName, setEditName] = useState<{ appId: string; value: string; after: string }>({
    appId: undefined,
    value: '',
    after: '',
  });
  const [breadcrumItems, setBreadcrumItems] = useState<any[]>([
    {
      folderId: 'index',
      folderName: '主页',
    },
  ]);
  const [createFolderOpen, setCreateFolderOpen] = useState(false);
  // 移至分组弹窗
  const [folderMoveModal, setFolderMoveModal] = useState<{
    visible: boolean;
    fromFolderId: string;
    appId: string;
  }>({
    visible: false,
    fromFolderId: '',
    appId: '',
  });
  // 授权管理弹窗
  const [authorizeModal, setAuthorizeModal] = useState<{
    visible: boolean;
    selectedForms: any[];
  }>({
    visible: false,
    selectedForms: [],
  });
  const [showInput, setShowInput] = useState(false);
  console.log('folderAppList', folderAppList);
  const saveName = () => {
    setEditName({ appId: undefined, value: '', after: '' });
  };

  const geneModal = (title, onOk) => {
    return Modal.confirm({
      centered: true,
      title,
      onOk,
    });
  };

  const handleStatus = async (e, item) => {
    e.stopPropagation();
    const { enable, formVersionId, stashFormVersionId } = item;
    const exucteFn = async () => {
      const api = enable ? 'updateFormApplyDisable' : 'updateFormApplyOpen';
      await formManageApi[api](enable ? formVersionId : stashFormVersionId ?? formVersionId, { postId: baseInfo.postId });
      message.success('操作成功');
      fetchFolderAppList(baseInfo?.postId);
      setDropVisable(false);
    };
    geneModal(`确定${enable ? '禁用' : '开启'}该表单吗？`, exucteFn);
  };

  const handleDelete = async (e, item) => {
    e.stopPropagation();
    setDropVisable(false);
    const { formVersionId } = item;
    const exucteFn = async () => {
      const res = await formManageApi.deleteFormApply(formVersionId, baseInfo.postId);
      message.success('删除成功');
      fetchFolderAppList(baseInfo?.postId);
    };
    geneModal(`确定删除该表单吗？`, exucteFn);
  };

  const sortForms = async (values) => {
    await formManageApi.updateFormSort({
      orderType: 'FORM_AUTH_SORT',
      formSortList: values.map((item, index) => ({
        formVersionId: item.formVersionId,
        orderNum: index + 1,
      })),
    });
    fetchFolderAppList(baseInfo?.postId);
  };

  const handleTopForm = async (item) => {
    const { formVersionId } = item;
    await formManageApi.updateFormTop({
      formVersionId,
      topType: 'FORM_AUTH_SORT',
    });
    message.success('操作成功');
    fetchFolderAppList(baseInfo?.postId);
  };

  const handleCancelTopForm = async (item) => {
    const { formVersionId } = item;
    await formManageApi.updateFormCancelTop({
      formVersionId,
      topType: 'FORM_AUTH_SORT',
    });
    message.success('操作成功');
    fetchFolderAppList(baseInfo?.postId);
  };

  const navigateToFolder = (item) => () => {
    setBreadcrumItems((pre) => {
      const index = pre.findIndex((i) => i.folderId === item.folderId);
      return pre.slice(0, index + 1);
    });
    if (item?.folderId === 'index') {
      // onFolderFilter({
      //   parentId: '',
      //   appList: [],
      // });
      // fetchFolderAppList();
    } else {
      // onFolderFilter({
      //   parentId: item?.folderId,
      //   appList: [],
      // });
    }
  };

  // 核准配置
  const handleVerifiedConfig = (item) => {
    console.log('handleVerifiedConfig', item);
  };

  const findNodeByFolderId = (tree, folderId) => {
    const findNode = (node) => (node.folderId === folderId ? node : node.childList?.map(findNode).find((child) => child) || null);
    return findNode(tree);
  };
  const currentFoldObj = useMemo(() => {
    // 遍历集团的每个岗位，一个岗位对应一颗树，递归树查找节点
    const appFolderByPosts = get(folderAppList, [0, 'children']) || [];
    let node = null;
    for (let index = 0; index < appFolderByPosts.length; index++) {
      const element = appFolderByPosts[index] || null;
      const appFolderVO = element?.appFolderVO || null;
      node = findNodeByFolderId({ ...appFolderVO, folderId: 'index' }, breadcrumItems[breadcrumItems.length - 1].folderId);
      if (!isEmpty(node)) break;
    }
    return node?.folderId === 'index' ? null : node;
  }, [breadcrumItems, folderAppList]);
  const generateMenuItems = (itemInfo): MenuProps['items'] => {
    return [
      {
        label: (
          <>
            <Button
              type="text"
              onClick={() => {
                setCreateFolderOpen(false);
                // createNewFolder();
              }}
              icon={<FolderOutlined />}
            >
              新建文件夹
            </Button>
          </>
        ),
        key: '_create-folder_',
      },
    ];
  };

  const items = (item): MenuProps['items'] => {
    const isStash = item.status === MetaAppStatus.Stash;
    const isTop = item.authIfTop; // 置顶为0
    return [
      // {
      //   key: 'move',
      //   label: (
      //     <a
      //       onClick={(e) => {
      //         setDropVisable(false);
      //         const fromFolderId = breadcrumItems[breadcrumItems.length - 1]?.folderId;
      //         e.stopPropagation();
      //         setFolderMoveModal((pre) => ({
      //           ...pre,
      //           visible: true,
      //           fromFolderId: fromFolderId === 'index' ? '' : fromFolderId,
      //           appId: item?.id,
      //         }));
      //       }}
      //     >
      //       移至分组
      //     </a>
      //   ),
      //   icon: <FolderOutlined />,
      // },
      String(item.enable) === ActionType.ENABLE && {
        key: 'authorize',
        label: (
          <a
            onClick={(e) => {
              setDropVisable(false);
              e.stopPropagation();
              setAuthorizeModal({ visible: true, selectedForms: [{ id: item.formVersionId, name: item.name }] });
            }}
          >
            授权管理
          </a>
        ),
        icon: <AuditOutlined />,
      },
      {
        key: 'verifiedConfig',
        label: (
          <a
            onClick={(e) => {
              setDropVisable(false);
              e.stopPropagation();
              setCheckConfigModal({ open: true, formData: item });
            }}
            href="javascript:void(0)"
          >
            核准配置
          </a>
        ),
        icon: <FileProtectOutlined />,
      },
      !isTop && {
        key: 'top',
        label: (
          <a
            onClick={(e) => {
              setDropVisable(false);
              e.stopPropagation();
              handleTopForm(item);
            }}
          >
            置顶
          </a>
        ),
        icon: <VerticalAlignTopOutlined />,
      },
      isTop && {
        key: 'cancelTop',
        label: (
          <a
            onClick={(e) => {
              setDropVisable(false);
              e.stopPropagation();
              handleCancelTopForm(item);
            }}
          >
            取消置顶
          </a>
        ),
        icon: <VerticalAlignBottomOutlined />,
      },
      !isStash && {
        key: '4',
        label: <a onClick={(e) => handleStatus(e, item)}>表单{String(item.enable) === ActionType.ENABLE ? '禁用' : '启用'}</a>,
        icon: item.status === ActionType.ENABLE ? <StopOutlined /> : <CheckCircleOutlined />,
      },
      String(item.enable) !== ActionType.ENABLE && {
        key: '5',
        label: <a onClick={(e) => handleDelete(e, item)}>删除表单</a>,
        icon: <DeleteOutlined />,
      },
    ].filter(Boolean);
  };
  const renderBreadcrumb = useMemo(() => {
    if (breadcrumItems.length <= 1) return null;
    return (
      <Breadcrumb style={{ marginBottom: 16, padding: '0 20px' }} className={styled.breadcrumbWrapper}>
        {breadcrumItems &&
          breadcrumItems.map((item, index) => {
            if (index === breadcrumItems.length - 1) {
              return <Breadcrumb.Item key={item.folderId}>{item.folderName}</Breadcrumb.Item>;
            }
            return (
              <Breadcrumb.Item onClick={navigateToFolder(item)} href="#" key={item.folderId}>
                {item.folderName}
              </Breadcrumb.Item>
            );
          })}
      </Breadcrumb>
    );
  }, [breadcrumItems]);
  const renderCardApp = (item: any) => {
    const { formVersionId, appType, name, postName, userName, userId, createTime, status, enable, folderId } = item;
    const isExcuteForm = appType === 'SUBMIT';
    if (folderId) {
      return (
        <div
          draggable={!showInput}
          onDragStart={(e) => {
            e.dataTransfer.setData('text', '');
          }}
          onDragOver={(e) => {
            e.preventDefault();
          }}
          onDrop={(e) => {
            const dragId = e.dataTransfer.getData('text');
            const dropId = item?.folderId;
            // 调用事件
            console.log('dragId,dropId in folder', dragId, dropId);
            // dropApp(dragId, dropId);
          }}
        >
          <FolderCard
            postId={baseInfo?.postId}
            group={item}
            showInput={showInput}
            setShowInput={setShowInput}
            fetchFolderAppList={() => fetchFolderAppList(baseInfo.postId)}
            onOpenGroup={() => {
              // handleOpenGroup(item);
            }}
          />
        </div>
      );
    }
    return (
      <div
        className={styled.cardWrapper}
        onDoubleClick={() => {}}
        onClick={async () => {
          setClickFieldId(item?.formVersionId);
          // openFormViewDetail(bridge, {
          //   hideInMenu: true,
          //   state: {
          //     ...item,
          //     ...(baseInfo || {}),
          //   },
          // });
          openAppDesigner(bridge, {
            hideInMenu: true,
            state: {
              ...item,
              post: baseInfo?.selectedPost,
            },
          });
        }}
        key={formVersionId}
        // !editName.appId
        draggable={false}
        onDragStart={(e) => {
          e.dataTransfer.setData('text', item?.id);
        }}
        onDragOver={(e) => {
          e.preventDefault();
        }}
      >
        <header className={styled.headerBox}>
          <img src={isExcuteForm ? IconFormExcute : IconFormApprove} width={31} height={36} />
          <div style={{ flex: 1, overflow: 'hidden' }}>
            <article className={styled.headerInfo}>
              {editName.appId === formVersionId ? (
                <Input
                  autoFocus
                  size="small"
                  value={editName.value}
                  onChange={(e) => setEditName({ ...editName, value: e.target.value })}
                  onBlur={saveName}
                  addonAfter={editName.after}
                />
              ) : (
                <div className={styled.titleBox}>
                  <span
                    className={styled.title}
                    onClick={(e) => {
                      setEditName({ appId: formVersionId, value: name, after: '' });
                    }}
                  >
                    <TextEllipsisTooltip text={name} />
                  </span>
                  <div className={styled.tagBox}>
                    {item.authIfTop && (
                      <Tag color="blue">置顶</Tag>
                      // <div
                      //   className={styled.tag}
                      //   style={{
                      //     border: '1px solid #e8f3ff',
                      //     color: '#4c7bf6',
                      //     background: '#e8f3ff',
                      //   }}
                      // >
                      //   置顶
                      // </div>
                    )}
                    {tagType[String(enable)]?.label && <Tag color={tagType[String(enable)]?.color}>{tagType[String(enable)]?.label}</Tag>}

                    {/* <div
                      className={styled.tag}
                      style={{
                        color: tagType[String(enable)]?.color,
                        backgroundColor: tagType[String(enable)]?.backgroundColor,
                        border: tagType[String(enable)]?.border,
                      }}
                    >
                      {tagType[String(enable)]?.label}
                    </div> */}
                    {status === MetaAppStatus.Stash && (
                      <Tag color={executeTagType?.[status]?.color}>{executeTagType?.[status]?.label}</Tag>
                      // <div
                      //   className={styled.tag}
                      //   style={{
                      //     color: executeTagType?.[status]?.color,
                      //     backgroundColor: executeTagType?.[status]?.backgroundColor,
                      //     border: `1px solid ${executeTagType?.[status]?.color}`,
                      //     minWidth: '44px',
                      //   }}
                      // >
                      //   {executeTagType?.[status]?.label}
                      // </div>
                    )}
                  </div>
                </div>
              )}
            </article>
            <div className={styled.timeLabel}>
              <span>{postName}</span>
              {userId && <span>/{userName}</span>}
            </div>
          </div>
        </header>
        <Divider style={{ margin: 0 }} />
        <aside className={styled.contentBox}>
          {/* <div className={styled.infoBox}></div> */}
          <div className={styled.footerBox}>
            <div className={styled.timeBox}>
              <span>创建时间：</span>
              <span>{createTime}</span>
            </div>
            {items(item)?.length > 0 && (
              <Dropdown
                menu={{ items: items(item) }}
                placement="bottomCenter"
                open={dropVisable && item?.formVersionId === clickFieldId}
                onOpenChange={() => setDropVisable(!dropVisable)}
                trigger={['click']}
              >
                <Button
                  type="text"
                  size="small"
                  style={{ fontSize: '12px', lineHeight: '0', padding: 0 }}
                  onClick={(e) => {
                    // setDropVisable(true);
                    e.stopPropagation();
                    setClickFieldId(item?.formVersionId);
                    setDropVisable(true);
                  }}
                >
                  ···
                </Button>
              </Dropdown>
            )}
          </div>
        </aside>
      </div>
    );
  };

  const renderList = (folderAppList) => {
    return (
      <div style={{ position: 'relative', overflowY: 'auto', height: '100%' }}>
        {/* inFolder ? '0 30px 30px' : */}
        {folderAppList.length > 0 &&
          folderAppList?.map((item, index) => {
            const isSubmit = item?.appType === 'SUBMIT';
            const defaultKey = isSubmit ? 'SUBMIT-APP' : 'APPLY-APP';
            const isEmpty = item?.list?.length === 0;
            if (isEmpty) {
              return null;
            }
            return (
              <Collapse
                key={index}
                ghost
                defaultActiveKey={['SUBMIT-APP', 'APPLY-APP']}
                bordered={false}
                style={{ marginBottom: 16 }}
                expandIcon={({ isActive }) => <CaretRightOutlined rotate={isActive ? 90 : 0} />}
                collapsible={'header'}
              >
                <Collapse.Panel
                  header={
                    <div>
                      {/* {`${baseInfo?.selectedPost?.orgName}-${baseInfo?.selectedPost?.postName}`} */}
                      {isSubmit ? '执行表单' : '管理表单'}
                    </div>
                  }
                  extra={
                    <div style={{ position: 'absolute', right: 0, top: '-6px' }}>
                      <BatchFormSetting appList={folderAppList} />
                      <span style={{ color: '#4b8afb' }}>|</span>
                      <SortList
                        list={item?.list}
                        idKeyName="formVersionId"
                        namerKeyName="name"
                        onOk={(value) => {
                          // console.log(value, 'value--------');
                          sortForms(value);
                        }}
                      />
                    </div>
                  }
                  key={defaultKey}
                >
                  <div className={styled.listWrapper}>{item?.list?.map((item) => renderCardApp(item))}</div>
                </Collapse.Panel>
              </Collapse>
            );
          })}

        {/* </div> */}
      </div>
    );
  };

  return (
    <>
      {folderAppList.length > 0 ? (
        <div style={{ width: '100%', height: '100%', overflowY: 'auto', padding: '0 20px' }}>
          {renderBreadcrumb}
          <Dropdown
            menu={{ items: generateMenuItems(folderAppList) }}
            trigger={['contextMenu']}
            open={false}
            onOpenChange={(open) => {
              setCreateFolderOpen(open);
            }}
          >
            {renderList(
              !isEmpty(currentFoldObj)
                ? currentFoldObj
                : [
                    {
                      appType: 'APPROVE',
                      key: 'APPROVE_APP',
                      list: cloneDeep(folderAppList)?.filter((app) => app?.appType === 'APPROVE'),
                    },
                    {
                      appType: 'SUBMIT',
                      key: 'SUBMIT_APP',
                      list: cloneDeep(folderAppList)?.filter((app) => app?.appType === 'SUBMIT'),
                    },
                  ],
            )}
            {/* <>
              {
                renderList(cloneDeep(folderAppList)?.filter(app => app?.appType === 'APPROVE'))
              }
              {
                renderList(cloneDeep(folderAppList)?.filter(app => app?.appType === 'SUBMIT'))
              }
            </> */}
          </Dropdown>
        </div>
      ) : (
        <Empty description="暂无数据" />
      )}

      {folderMoveModal.visible && (
        <FolderMoveModal
          // folderMoveModal={folderMoveModal}
          fromFolderId={folderMoveModal.fromFolderId}
          appId={folderMoveModal.appId}
          postId={baseInfo.postId}
          onCancel={() => {
            setFolderMoveModal({
              visible: false,
              fromFolderId: null,
              appId: null,
            });
          }}
          onConfirm={() => {
            setFolderMoveModal({
              visible: false,
              fromFolderId: null,
              appId: null,
            });
            fetchFolderAppList(baseInfo.postId);
          }}
          // setFolderMoveModal={setFolderMoveModal}
          // folderAppList={folderAppList}
          // handleAppClick={handleAppClick}
        />
      )}

      {authorizeModal.visible && (
        <AuthorizationManagement
          selectedForms={authorizeModal.selectedForms}
          onOk={() => {}}
          onCancel={() => {
            setAuthorizeModal({ visible: false, selectedForms: [] });
          }}
        />
      )}
    </>
  );
};

export default CardItem;
