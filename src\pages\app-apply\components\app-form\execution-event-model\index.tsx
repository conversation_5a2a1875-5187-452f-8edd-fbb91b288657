import { ExecutionEventType, PostEntryType } from '@/pages/app-designer/components/right-panel/form-design/button-options/constants';
import { FormButtonConfig, FormButtonEvent } from '@/services/form-button';
import { Form, Modal } from '@gwy/components-web';
import EntryPostAuto from './components/entry-post-auto';
import EntryPostRadio from './components/entry-post-radio';

interface IProps {
  btnItem: FormButtonConfig;
  handelCancel: () => void;
  handleOk: (values) => void;
}

const ExecutionEventModel = ({ btnItem, handelCancel, handleOk }: IProps) => {
  const [form] = Form.useForm();
  const postEntryTypeList = btnItem?.events?.[0]?.postEntryOrLevelConfig?.postEntryTypeList;
  const eventType = btnItem?.events?.[0]?.eventType;

  const onOk = async () => {
    const { postEntryType, entryPostType, entryDeptId, entryPostName } = await form.validateFields();
    handleOk({
      entryEventData: {
        postEntryType,
        entryPostType,
        entryDeptId,
        entryPostName,
      },
    });
  };

  const renderContent = (event: FormButtonEvent) => {
    if ([ExecutionEventType.POST_ENTRY, ExecutionEventType.POST_INVITE].includes(event?.eventType)) {
      if (event?.postEntryOrLevelConfig?.postEntryTypeList) {
        return <EntryPostAuto isAuto={event.postEntryOrLevelConfig.postEntryTypeList[0] === PostEntryType.AUTO_CREATE} />;
      } else if (!event?.ifAutoSet) {
        return <EntryPostAuto isAuto={false} />;
      } else {
        return <EntryPostAuto isAuto />;
      }
    }
    // if (event?.eventType === ExecutionEventType.POST_LEVEL) {
    //   return <LevelPost form={form} btnItem={btnItem} />;
    // }
    return null;
  };

  return (
    <Modal open title={`入职选择`} onOk={onOk} onCancel={handelCancel} width={500} closable={false} styles={{ body: { height: 400 } }}>
      {[ExecutionEventType.POST_ENTRY, ExecutionEventType.POST_INVITE].includes(eventType) && postEntryTypeList && postEntryTypeList.length === 2 ? (
        <EntryPostRadio form={form} onClose={handelCancel} />
      ) : (
        <>
          <Form form={form} labelCol={{ span: 6 }} wrapperCol={{ span: 16 }} labelAlign="left">
            {(btnItem?.events || []).map((item) => renderContent(item))}
          </Form>
        </>
      )}
    </Modal>
  );
};

export default ExecutionEventModel;
