import { CalcType, ExpressionType, FieldType, ValueType } from '@/types/calc-field';

export const validateConditionGroups = (conditionGroups: any[]): boolean => {
  if (!conditionGroups || conditionGroups.length === 0) return false;

  for (const group of conditionGroups) {
    if (group.conditionItems && group.conditionItems.length > 0) {
      return true;
    }
  }
  return false;
};

/**
 * 校验标签的筛选条件
 * @param tagConfig 标签配置对象
 * @param tagName 标签名称，用于错误提示
 * @param formulaType 公式类型，用于错误提示
 */
export const validateTagConditions = (tagConfig: any, tagName: string, formulaType: string = '公式配置'): void => {
  // 检查 refTag 中的筛选条件
  const hasValidConditions = validateConditionGroups(tagConfig.refTag.conditionGroups);
  if (!hasValidConditions) {
    throw new Error(`独立字段的${formulaType}中，标签"${tagName}"的筛选条件不能为空，请配置有效的筛选条件`);
  }
};

/**
 * 校验公式计算数组中的标签筛选条件
 * @param formulasCalcs 公式计算数组
 * @param formulaType 公式类型，用于错误提示
 */
export const validateFormulasCalcs = (formulasCalcs: any[], formulaType: string = '公式配置'): void => {
  if (!formulasCalcs || formulasCalcs.length === 0) return;

  for (const formula of formulasCalcs) {
    // 检查直接的标签配置（refTag）
    if (formula.expressionType === ExpressionType.TAG) {
      const tagName = formula.showValue || formula.refTag?.name || '未知标签';
      validateTagConditions(formula, tagName, formulaType);
    }

    // 检查公式中的标签配置（refFunction.arguments）
    if (formula.refFunction?.arguments) {
      for (const arg of formula.refFunction.arguments) {
        if (arg.expressionType === ExpressionType.TAG) {
          const tagName = arg.showValue || arg.refTag?.name || '未知标签';
          validateTagConditions(arg, tagName, formulaType);
        }
      }
    }
  }
};

/**
 * 校验阶梯计算中的公式配置
 * @param calculationCalcs 阶梯计算配置数组
 */
export const validateCalculationFormulas = (calculationCalcs: any[]): void => {
  for (const calc of calculationCalcs) {
    // 检查每个规则组中的计算模式
    if (calc.calculationModes && Array.isArray(calc.calculationModes)) {
      for (const mode of calc.calculationModes) {
        // 检查条件公式 (conditionFormulas)
        if (mode.conditionFormulas?.formulasCalcs) {
          validateFormulasCalcs(mode.conditionFormulas.formulasCalcs, '阶梯计算条件公式');
        }

        // 检查结果公式 (resultFormulas) - 当结果类型为计算公式时
        if (mode.valueType === ValueType.CALC_FORMULAS && mode.resultFormulas?.formulasCalcs) {
          validateFormulasCalcs(mode.resultFormulas.formulasCalcs, '阶梯计算结果公式');
        }
      }
    }

    // 检查规则组的触发条件（conditionGroups）
    // if (calc.triggerCondition && calc.conditionGroups) {
    //   const hasValidConditions = validateConditionGroups(calc.conditionGroups);
    //   if (!hasValidConditions) {
    //     throw new Error('独立字段的阶梯计算中，规则组的触发条件不能为空，请配置有效的筛选条件');
    //   }
    // }
  }
};

/**
 * 校验独立字段的公式配置
 * 当字段类型为独立字段时，需要保证公式配置中配置的标签的筛选条件中 conditionGroups 中的 conditionItems 不为空
 * @param values 表单值对象
 */
export const validateIndependentFieldFormulas = (values: any): void => {
  if (values.fieldType === FieldType.IndependentField) {
    if (values.calcType === CalcType.FormulasCalc) {
      // 公式计算校验
      const { formulasCalcs = [] } = values;
      validateFormulasCalcs(formulasCalcs, '公式计算');
    } else if (values.calcType === CalcType.Calculation) {
      // 阶梯计算校验
      const { calculationCalcs = [] } = values;
      validateCalculationFormulas(calculationCalcs);
    }
  }
};
