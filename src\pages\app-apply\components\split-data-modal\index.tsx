import { datasourceDataAPI } from '@/services';
import { genUuid } from '@/utils';
import { getMetaTagUniqueId } from '@/utils/metadata';
import { Form, message, Modal } from '@gwy/components-web';
import { cloneDeep } from 'lodash-es';
import { createContext, useEffect, useMemo, useState } from 'react';
import { DATATYP_SPLTE_EENUM, SPILTETYPEENUM } from '../app-form';
import { SplitType } from './components/edit-cell';
import SplitDataTable from './components/split-table';

export const DataSplitTableContext = createContext<any>(null);
export const useForceUpdate = () => {
  const [ticket, setTicket] = useState<any>();
  return () => {
    setTicket(Math.random());
  };
};
interface Iprops {
  records: any;
  cFields: any;
  cTagsMap: any;
  formVersion: any;
  onCancle: () => void;
  onOk: (data?: any) => void;
  post: any;
  dataId: any;
}
const SplitDataModal = (props) => {
  const { onOk: closePage, onCancel, records, cFields, cTagsMap, formVersion, post, dataId } = props;
  const [form] = Form.useForm();
  const forceUpdate = useForceUpdate();
  const [initDataUnits, setInitDataUnits] = useState<any[]>([]);
  console.log(records, cFields, cTagsMap, formVersion, 'split-modal-props');
  const [cDataUnits] = useMemo(() => {
    let values = {};
    const cDataUnits = formVersion?.dataUnits
      .map((item) => {
        // const record = records.find((record) => record.dataUnitId === item.dataUnitId)?.record;
        // console.log(record, records,'record------------');
        const tags = formVersion?.extConfig.fields?.filter((tag) => tag.dataUnitId === item?.dataUnitId);
        const isAllOutput = tags.every((tag) => tag.readonly);
        if (isAllOutput) {
          return undefined;
        }
        // debugger;
        return {
          ...item,
          datasource: records,
          tags: tags.map((field) => ({
            ...field,
            ...(cTagsMap[getMetaTagUniqueId(field)] || {}),
            // getMetaTagUniqueId(field): {field.readonly ? record[field.code] : record[field.code]?.data},
            // values[] : {
            //     value: field.readonly ? record[field.code] : record[field.code]?.data,
            //   }
          })),
        };
      })
      ?.filter(Boolean);
    return [cDataUnits];
  }, [records, formVersion, cTagsMap, cFields]);
  const unitToDataIdMap = useMemo(() => {
    const record = records?.[0] || {};
    const map = {};
    formVersion?.dataUnits?.forEach((dataUnit) => {
      let codeKey = '_id';
      if (!dataUnit?.mainDataUnit) {
        codeKey = `${dataUnit.dataUnitId}__id`;
      }
      map[dataUnit.dataUnitId] = record?.[codeKey];
    });
    // console.log(map, 'map----------')
    return map;
  }, [records, formVersion]);

  const geneRowData = (values) => {
    const mainDataUnit = cDataUnits?.find((item) => item?.mainDataUnit);
    console.log(values, cDataUnits, mainDataUnit, 'values-----');
    const dataUnits = values?.apps?.[0]?.dataUnits;
    console.log(dataUnits, 'dataUnits---------');
    // 形成对象数组
    let results = [];
    (dataUnits || [])?.forEach((unitItem) => {
      // const checkRow = unitItem?.multiRows > 1 ? (unitItem?.multiRows).slice(1, unitItem?.multiRows?.length).every(rowItem => rowItem) : false
      const tags = cDataUnits?.find((cDataUnit) => cDataUnit?.dataUnitId === unitItem?.dataUnitId)?.tags;
      let checkFlag = false;
      if (unitItem?.multiRows.length > 1) {
        const checkArr = unitItem?.multiRows.slice(1, unitItem?.multiRows?.length);
        checkFlag =
          checkArr.every((row) => row.buttonType === 'AGREE') ||
          checkArr.every((row) => row.buttonType === 'REFUSE') ||
          checkArr.every((row) => row.buttonType === 'RETURN');
      }
      const { multiRows } = unitItem || {};
      // console.log('multiRows-------', multiRows);
      const isMain = unitItem.dataUnitId === mainDataUnit?.dataUnitId;

      const checkSplit = unitItem.splitType === SplitType.SPLIT;
      multiRows.forEach((rowItem, rowIndex) => {
        // console.log('rowItem-------', rowItem);
        const buildData = {
          preDataId: unitToDataIdMap[unitItem.dataUnitId],
          dataUnitId: unitItem.dataUnitId,
          groupId: genUuid(),
          formDataOperateType: rowItem.buttonType ? rowItem.buttonType : checkFlag ? multiRows[1]?.buttonType : null,
          splitType: checkSplit || dataUnits?.length === 1 ? SPILTETYPEENUM.DATA_SPLIT : undefined,
          dataType:
            !checkSplit && dataUnits?.length > 1
              ? undefined
              : !rowItem.buttonType || rowIndex === 0
              ? DATATYP_SPLTE_EENUM?.MAIN
              : DATATYP_SPLTE_EENUM?.SUB,
        };
        const tagDatas = cloneDeep(tags)
          ?.filter((tag) => !tag?.readonly)
          .map((tag) => {
            let codeKey = isMain ? tag.code : `${tag.dataUnitId}_${tag.code}`;
            if (rowIndex !== 0 && !tag.readonly) {
              // 说明是输入类型,不需要再判断了
              codeKey = getMetaTagUniqueId(tag);
              tag['value'] = rowItem[codeKey]?.value;
              return tag;
            }
            console.log(rowItem[codeKey], rowItem, codeKey, 'rowItemvalue--------');
            tag['value'] = rowItem[codeKey]?.data;
            return tag;
          });
        // const handletags = cloneDeep(tagDatas);
        // console.log(tagDatas, 'tagDatas----------')
        results.push({
          ...buildData,
          tagDatas: rowIndex === 0 ? [] : tagDatas,
        });
      });
    });
    // console.log(results, 'results----------')
    return results;
  };

  const handleOk = async () => {
    const values = await form.validateFields();
    // const buildData = {};
    const buildRowDatas = await geneRowData(values);
    const params = {
      postId: post?.postId,
      formId: formVersion.formId,
      formVersionId: formVersion.formVersionId,
      preDataId: dataId,
      rowDatas: buildRowDatas,
    };
    console.log(params, 'params----------');
    Modal.confirm({
      title: `确定提交数据吗？`,
      onOk: async () => {
        try {
          await datasourceDataAPI.examineFormData(params);
          await message.success('操作成功', 1.5);
          // bridge.close();
          closePage();
        } catch (error) {}
      },
    });

    // console.log(params, 'params--------')
  };

  useEffect(() => {
    const dataUnits = cDataUnits.map((item) => ({
      multiRows: new Array(3).fill(item?.datasource?.[0]),
      splitType: SplitType.SPLIT,
    }));
    setInitDataUnits(dataUnits);
    form.setFieldValue('apps', [{ dataUnits }]);
  }, [formVersion, cDataUnits]);
  return (
    <Modal title="数据拆分" open onOk={handleOk} onCancel={onCancel}>
      <Form
        form={form}
        colon={false}
        scrollToFirstError
        onValuesChange={(values) => {
          console.log('values', values);
        }}
        onFieldsChange={(changeField) => {}}
      >
        {/* <Form.Item hidden name></Form.Item> */}
        <Form.List name="apps">
          {(fields, { add, remove }) => {
            // 表单中配置的数据单元
            // const dataUnits = new Array(3).fill(1)
            return fields.map((app, appIndex) => {
              return (
                <div key={appIndex}>
                  {/* <Form.Item name={[app.name, 'appId']}> </Form.Item> */}
                  <Form.List name={[app.name, 'dataUnits']}>
                    {() => {
                      return cDataUnits.map((unitItem, dataUnitIndex) => {
                        const unitTags = unitItem.tags;
                        return (
                          <DataSplitTableContext.Provider
                            value={{
                              form,
                              tags: unitTags,
                              appIndex,
                              dataUnitIndex,
                              forceUpdate,
                            }}
                            key={dataUnitIndex}
                          >
                            <SplitDataTable
                              tags={unitTags}
                              form={form}
                              dataUnit={unitItem}
                              initDataUnits={initDataUnits}
                              configDataUnits={cDataUnits}
                            />
                          </DataSplitTableContext.Provider>
                        );
                      });
                    }}
                  </Form.List>
                </div>
              );
            });
          }}
        </Form.List>
      </Form>
    </Modal>
  );
};

export default SplitDataModal;
