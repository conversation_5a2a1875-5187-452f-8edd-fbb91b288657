/* 表单授权审批 */
import { CDN_URL_DATASOURCE } from '@/const/common';
import { useBridge, useRoute } from '@/hooks';
import { departments as initialDepartments } from '@/pages/app-form-premission/components/authorization-management/const';
import { formatDateTime } from '@/utils';
import { InfoCircleOutlined, QuestionCircleOutlined } from '@ant-design/icons';
import {
  Avatar,
  Button,
  Checkbox,
  Collapse,
  Divider,
  Input,
  LayoutPage,
  message,
  Modal,
  Popover,
  TextEllipsisTooltip,
  Tooltip,
} from '@gwy/components-web';
import classNames from 'classnames';
import { memo, useCallback, useEffect, useState } from 'react';
import styles from './index.less';

// 所有岗位配置组件
const AllPostConfig = ({ approveInfo }) => {
  return (
    <div className={styles.permissionConfig}>
      <div className={styles.sectionHeader}>
        <div>
          <span className={styles.sectionTitle}>表单权限</span>
          <span className={styles.sectionDesc}>（管理范围内所有岗位拥有该表单）</span>
        </div>
        <a className={styles.viewConfigLink}>查看表单配置</a>
      </div>

      <Divider />

      <div className={styles.sectionTitle}>功能权限</div>
      <div className={styles.sectionContent}>顶级授权（绕过读写权限）：未开启</div>
    </div>
  );
};

// 指定岗位配置组件
const AssignPostConfig = ({ approveInfo }: { approveInfo: any }) => {
  const departmentData = [
    {
      key: '1',
      title: '技术开发部 (6人)',
      users: [
        { name: '张菲菲张菲菲张菲菲张菲菲张菲菲张菲菲张菲菲张菲菲', role: '执行岗', status: 'add' },
        { name: '张菲菲', role: '执行岗执行岗执行岗执行岗执行岗执行岗执行岗', status: 'add' },
        { name: '张菲菲', role: '执行岗', status: 'delete' },
        { name: '张菲菲', role: '管理岗', status: 'none', portraitUrl: null },
        { name: '张菲菲', role: '执行岗', status: 'none' },
        { name: '张菲菲', role: '执行岗', status: 'none' },
      ],
    },
    {
      key: '2',
      title: '测试运维部 (6人)',
      users: [
        { name: '张菲菲', role: '执行岗', status: 'add' },
        { name: '张菲菲', role: '执行岗', status: 'add' },
        { name: '张菲菲', role: '执行岗', status: 'delete' },
        { name: '张菲菲', role: '管理岗', status: 'none', portraitUrl: null },
        { name: '张菲菲', role: '执行岗', status: 'none' },
        { name: '张菲菲', role: '执行岗', status: 'none' },
      ],
    },
  ];

  // 新增/删除状态标签
  const getStatusTag = (status: 'add' | 'delete') => {
    return (
      <span
        className={classNames(styles.statusTag, {
          [styles.add]: status === 'add',
          [styles.delete]: status === 'delete',
        })}
      >
        {status === 'add' ? '新增' : status === 'delete' ? '删除' : ''}
      </span>
    );
  };

  // 移除岗位
  const onRemove = (user) => {
    Modal.confirm({
      title: '确认取消该岗位对应表单的权限？',
      centered: true,
      width: 340,
      onOk: async () => {
        try {
        } catch (error) {}
      },
    });
  };

  return (
    <div className={styles.permissionConfig}>
      <div className={styles.sectionHeader}>
        <div>
          <span className={styles.sectionTitle}>表单权限</span>
          <span className={styles.sectionDesc}>(以下岗位拥有该表单所使用的数列权限)</span>
        </div>
        <a className={styles.viewConfigLink}>查看表单配置</a>
      </div>

      <Collapse ghost defaultActiveKey={departmentData.map((i) => i.key)} className={styles.departmentCollapse}>
        {departmentData.map((dept) => (
          <Collapse.Panel key={dept.key} header={dept.title}>
            <div className={styles.userGrid}>
              {dept.users.map((user, index) => (
                <div key={index} className={styles.userItem}>
                  <div className={styles.userAvatar}>
                    <Avatar size={50} name={user.name} src={user.portraitUrl} />
                    {user.status !== 'none' && <div>{getStatusTag(user.status as 'add' | 'delete')}</div>}
                    <img className={styles.removeIcon} src={`${CDN_URL_DATASOURCE}/close-filled.png`} onClick={() => onRemove(user)} />
                  </div>
                  <div className={styles.userInfo}>
                    <div className={styles.userName}>
                      <TextEllipsisTooltip text={user.name} />
                    </div>
                    <div className={styles.userPost}>
                      <TextEllipsisTooltip text={user.role} />
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </Collapse.Panel>
        ))}
      </Collapse>

      <Divider />

      <div className={styles.sectionTitle}>功能权限</div>
      <div className={styles.sectionContent}>顶级授权（绕过读写权限）：未开启</div>
    </div>
  );
};

// 岗位类型配置组件
const PostTypeConfig = memo(({ approveInfo }: { approveInfo: any }) => {
  const [deptList, setDeptList] = useState(JSON.parse(JSON.stringify(initialDepartments)));

  // 计算部门的选中和半选状态
  const getDepartmentCheckState = (dept: any) => {
    const checkedCount = dept.positionTypes.filter((pt: any) => pt.checked).length;
    const totalCount = dept.positionTypes.length;
    if (checkedCount === 0) return { checked: false, indeterminate: false };
    if (checkedCount === totalCount) return { checked: true, indeterminate: false };
    return { checked: false, indeterminate: true };
  };

  return (
    <div className={styles.permissionConfig}>
      <div className={styles.sectionHeader}>
        <div>
          <span className={styles.sectionTitle}>表单权限</span>
          <span className={styles.sectionDesc}>（以下岗位拥有该表单所使用的数列权限）</span>
        </div>
        <a className={styles.viewConfigLink}>查看表单配置</a>
      </div>

      <div className={styles.positionTypeSection}>
        {deptList.map((dept) => {
          const checkState = getDepartmentCheckState(dept);
          return (
            <div key={dept.id} className={styles.departmentRow}>
              <Checkbox checked={checkState.checked} indeterminate={checkState.indeterminate} className={styles.departmentCheckbox} disabled>
                {dept.name}
              </Checkbox>
              <div className={styles.positionTypes}>
                {dept.positionTypes.map((pt, index) => (
                  <div key={index}>
                    <Checkbox key={pt.id} checked={pt.checked}>
                      {pt.name}
                    </Checkbox>
                    {!!pt.tooltipMsg && (
                      <Tooltip title={pt.tooltipMsg}>
                        <QuestionCircleOutlined className={styles.questionIcon} />
                      </Tooltip>
                    )}
                  </div>
                ))}
              </div>
            </div>
          );
        })}
      </div>

      <Divider />

      <div className={styles.sectionTitle}>功能权限</div>
      <div className={styles.sectionContent}>顶级授权（绕过读写权限）：未开启</div>
    </div>
  );
});

// 渲染右侧内容的主函数
const renderRightContent = (approveInfo: any) => {
  const { choiceType } = approveInfo || {};

  switch (choiceType) {
    case 0:
      return <AllPostConfig approveInfo={approveInfo} />;
    case 1:
      return <AssignPostConfig approveInfo={approveInfo} />;
    case 2:
      return <PostTypeConfig approveInfo={approveInfo} />;
    default:
      return <div className={styles.noData}>暂无数据</div>;
  }
};

const FormAuthorizationApprove = memo(() => {
  const bridge = useBridge();
  const route = useRoute();
  const { bizParam, refreshWaitingList } = (route as any).state || {};
  const { approveId } = bizParam || {};

  // 部门审批列表
  const [approveInfoList, setApproveInfoList] = useState([]);
  // 当前选中的部门审批信息
  const [currentApproveInfo, setCurrentApproveInfo] = useState<any>({});
  // 选中的部门审批ID列表
  const [approveIds, setApproveIds] = useState([]);

  // 获取审批信息
  const fetchApproveInfo = useCallback(async () => {
    const data = {
      approveUserId: 'c76349d3ca62412d9ce9a8060f3c723e',
      approveUserName: '熊玉英123',
      approvePostId: '19aa9a9e61e849ecb6ceb70b55de1e63',
      approvePostName: '管理员2',
      approveDepartmentId: 'af8a8c80607842caa1ca9fa6b4cfddac',
      approveDepartmentName: '3331',
      approveOrgId: '3331',
      approveOrgShortName: null,
      submitTime: '09-01 15:52',
      formAuthInfoVOS: [
        {
          authId: '68b55099e50ff20007f8cc53',
          appId: '08a992df7b7e495793e2f83871f83966',
          appName: '批量配置',
          createUserId: 'c76349d3ca62412d9ce9a8060f3c723e',
          createUserName: '熊玉英123',
          createPostId: '19aa9a9e61e849ecb6ceb70b55de1e63',
          createPostName: '管理员2',
          createPost: {
            postId: '19aa9a9e61e849ecb6ceb70b55de1e63',
            orgId: '65af38ce65220500072055de',
            orgName: '测试大集团',
            orgShortName: '3331',
            orgType: 1,
            organizationId: 'af8a8c80607842caa1ca9fa6b4cfddac',
            organizationName: '3331',
            departmentId: 'af8a8c80607842caa1ca9fa6b4cfddac',
            departmentName: '3331',
            deptType: 1,
            deptProperty: 1,
            parentId: 'eee0956052624e47b5ac909e37b34385',
            postName: '管理员2',
            userOrganizationTypeId: null,
            userId: 'c76349d3ca62412d9ce9a8060f3c723e',
            userName: '熊玉英',
            portraitUrl: 'http://*********:8089/avatar/o/2321/10002321.jpg?1688460932490?time=1688460934956',
            postType: 1,
            relateOrgId: '',
            relatePostId: '',
            relateType: 0,
            postProperty: 3,
            deletedFlag: 0,
            ifDisplace: false,
            time: null,
            description: '',
            ifMainPost: false,
            ifOrganizationMainPost: false,
            personnelType: 0,
            blocId: '64ffd7a15908010007ad7533',
            blocOrganizationId: 'af8a8c80607842caa1ca9fa6b4cfddac',
            blocOrganizationName: '3331',
            num: 2,
            blockFlag: null,
            toPostTime: null,
            toPostUserId: null,
            jobType: null,
            postStatus: null,
            hierarchy: 0,
            organizationTypeId: '',
            phone: '18713635458',
            positionType: 3,
            ifCancelPart: false,
            inputApproval: 0,
            ifTopApprove: true,
            ifAdminApprove: true,
            hostingStatus: 0,
            hostingPostId: null,
            belongBlocId: '64ffd7a15908010007ad7533',
            userPostType: null,
            sex: 1,
            ifRetract: true,
            mergeFlag: true,
            retractOrgId: '62e0fcade21b84000106ae71',
          },
          createTime: '04-17 15:25',
          departmentIds: null,
          postIds: [],
          postId: '19aa9a9e61e849ecb6ceb70b55de1e63',
          addDepartmentIds: null,
          cancelDepartmentIds: null,
          addPostIds: [],
          cancelPostIds: [],
          status: 1,
          appApproveSetDTOS: null,
          openPostAuthorize: false,
          openAutoApprove: false,
          ruleWhereFilters: null,
          dynamicWhereFilters: null,
          selectCopyPostIds: [],
          addCopyPostIds: [],
          cancelCopyPostIds: [],
          hiddenInTransaction: false,
          belongDepartmentId: 'af8a8c80607842caa1ca9fa6b4cfddac',
          choiceType: 1,
          filterOutCorporations: 0,
          postTypes: [1, 6, 12, 17],
          mangeAuths: [2],
          useGuest: null,
          openGuestAuth: null,
          guestUserIds: null,
          guestUserChanges: [],
          dataAuthorizationDetailDTOList: null,
          useType: 2,
        },
        {
          authId: '68b550afe50ff20007f8cc5b',
          appId: '1ea4ec2c1c0c4ef0be600bde5ce05ebf',
          appName: '计算字段',
          createUserId: 'c76349d3ca62412d9ce9a8060f3c723e',
          createUserName: '熊玉英',
          createPostId: '19aa9a9e61e849ecb6ceb70b55de1e63',
          createPostName: '管理员2',
          createPost: {
            postId: '19aa9a9e61e849ecb6ceb70b55de1e63',
            orgId: '65af38ce65220500072055de',
            orgName: '测试大集团',
            orgShortName: '3331',
            orgType: 1,
            organizationId: 'af8a8c80607842caa1ca9fa6b4cfddac',
            organizationName: '3331',
            departmentId: 'af8a8c80607842caa1ca9fa6b4cfddac',
            departmentName: '3331',
            deptType: 1,
            deptProperty: 1,
            parentId: 'eee0956052624e47b5ac909e37b34385',
            postName: '管理员2',
            userOrganizationTypeId: null,
            userId: 'c76349d3ca62412d9ce9a8060f3c723e',
            userName: '熊玉英',
            portraitUrl: 'http://*********:8089/avatar/o/2321/10002321.jpg?1688460932490?time=1688460934956',
            postType: 1,
            relateOrgId: '',
            relatePostId: '',
            relateType: 0,
            postProperty: 3,
            deletedFlag: 0,
            ifDisplace: false,
            time: null,
            description: '',
            ifMainPost: false,
            ifOrganizationMainPost: false,
            personnelType: 0,
            blocId: '64ffd7a15908010007ad7533',
            blocOrganizationId: 'af8a8c80607842caa1ca9fa6b4cfddac',
            blocOrganizationName: '3331',
            num: 2,
            blockFlag: null,
            toPostTime: null,
            toPostUserId: null,
            jobType: null,
            postStatus: null,
            hierarchy: 0,
            organizationTypeId: '',
            phone: '18713635458',
            positionType: 3,
            ifCancelPart: false,
            inputApproval: 0,
            ifTopApprove: true,
            ifAdminApprove: true,
            hostingStatus: 0,
            hostingPostId: null,
            belongBlocId: '64ffd7a15908010007ad7533',
            userPostType: null,
            sex: 1,
            ifRetract: true,
            mergeFlag: true,
            retractOrgId: '62e0fcade21b84000106ae71',
          },
          createTime: '03-25 14:07',
          departmentIds: [
            '2a1e9444568043a9850034eb3120e76e',
            'af8a8c80607842caa1ca9fa6b4cfddac',
            '2a0e5ade0d794b21b6963db42b63344f',
            '485504f53bad44ebb725578cf30c8626',
            '763b6be5012e488ba5c942f04c6bb2cf',
            'a16a05d3626c4d908b1df84ba871c47d',
            'b2a23d2e8b05482f8f82966014073de6',
            'b9633a6bb9284ec8b913e574fbe32f7c',
            'd91a262e4d454ded88ce6165983f5b03',
            'd951d1495a3c49dd93422aa6c088a32b',
            '344f1462b90f48e7bf1e9fc9087a634c',
            'f01d0b0b903d4d468975fe3c3fcdbcea',
            '2044fdef85954d2b9b8ad09aa8944395',
            'ca0924926a8c4050ae822e9295b6213c',
            '5311e633ce3f4a1dac43c84d9b2ac93a',
          ],
          postIds: [
            '37b499d45814493d887d296c7045cd03',
            '7ded3189f341410d9be1d10414cf71cc',
            '73bb66ebcd284ef0952ce30b20eeaae4',
            '3ce726425da242ed82e29dfaba5d4a48',
            '5b8100dabdb34fed88712c1f3b1218fa',
            'd1334268b746460f89a02dcfb347e8c4',
            '2ab2ed5a031d45ff862f7720d0738358',
            '0a09dbd07a2d429192129f5bf662cd4e',
            '12cb9f8579884ff4b121ec9b2a315afc',
            '5393d98b40554a188f97bb6d551c7333',
            '5ca70171137b4279a770326a128ec434',
            'a2c9f765e11645d9966e18331f11c65e',
            '92213d9717014958856e475433a4f647',
            '94c39f45a9d149048b57ebd8f4aeb23d',
            'ee91b7bb3ec442429880fd43e1f3d739',
            'eeb9430bae804280989537031b2c1dae',
            '67b81645ea5e480007c41040',
            '67b81645ea5e480007c4103f',
            'b231db7f58a94fa6b1934e2369c4ab5f',
            'f671c202b0644050b8dfb8fc69ec8199',
            'f51224dc12ed479cbca71df4bf68808a',
            '4db8587550db45a3a598c076116247b9',
            '66b7920b4d144c44a259ff91ee69f772',
            '20d81e2f307244a4b6e0893c3bd603bf',
            '671bd2ac9e414a6db10b2fdd4c8999ad',
            'e3728be00ef645e2b6bf6e3a3ffb76a9',
            '03100bf43ced460fab41ee2e97ad09ca',
            '2332eadf8ccc4b1c83190a53ab0ad6ec',
            'f9fc0475b92c42cbba25fdeff4dab69d',
            '343b9ca749384eb7b73e53c7a4ceafdb',
            '323c9c0fc8b3435ba9b82bff01a18d94',
            'bb9808d17be143f49fbe2f9248ba965e',
            'd6b8e51591e24822b818d0f8ee203b7a',
            '2a906f19a7cc44aa81fcc80caf3d811c',
            '3eb842a9f0a443e3b8c10bae354ddfd1',
            '65f3eb71c9e77c0007042549',
            '65f3eb71c9e77c0007042548',
            'd6788b0131a44d528af9ab26ce10ec58',
            '43a1ff920fe54760a9a529168ff7a402',
            '15caea1553334ebc81f7d99fa62dcee4',
            '69f5232d665d400291e8f3aaf2264980',
            '3ad29233304343c1a93ea40252318100',
            'a997993eab5b4123bfb82c4febdd9851',
            '1fbcc37ae3da44e9be4382a821afc61c',
            'dd1efb70155a46b09451cb88d763a470',
            '622713f6963843b1afff53967dc9ec7d',
            '63191fe116e44eb599a93bd7168ca66f',
            'bb1c7444b9b945398735c0daaf9bb75c',
            '5efb8039a9ce4175a79a00fe4344be67',
            'd2f68f1f9c7b4a009c138074ec99760b',
            'f1e6aa5ee7a34dd69f7fa06cda0349ec',
            '2a38d91881a74241b38088f3ecba0d08',
            '50c512221a2b43ac89635176403045b1',
            '6be194568f6b429f954d6a1a3adfc9bd',
            '796041a6252b47cdb1e44889043f103c',
            '85d33581d8384ed4805004a044ed445a',
            '4fee7f9855ff4dc9ae067ad4ef46b9a9',
            '670f2b39796c59000745dbcd',
            '670f2b39796c59000745dbce',
            '3cf7712e1f3e4c5fb325998800d20c7c',
            '10c18649c36f4b0a8e377af084a7c601',
            'a9e9f329c4594a5bbcc7b9e297a4909e',
            '670f21b906e80e0007311099',
            '670f21b906e80e000731109a',
            '3ff5aa859e764e7d90fe5166de16c682',
            '768f9e23a30448808bcc5029bacd95c3',
            '387bdac5d8d74c248828338c9e4bed8c',
            '670f23d406e80e0007311131',
            '670f23d406e80e0007311132',
            '41049abfba5b422aba96c68e584489be',
            '75e8cbe94dfe49e6a6e463080227d7b8',
            'ebbc4156623c4849accd2b3814976369',
            'a3a429df2555465ba81b5513f0532ac1',
            'b27257b15541473698cc8a613efd8cde',
            'e3bb43515c25454585b9acb8b8d1e9c4',
          ],
          postId: '19aa9a9e61e849ecb6ceb70b55de1e63',
          addDepartmentIds: null,
          cancelDepartmentIds: null,
          addPostIds: [],
          cancelPostIds: ['c5f6eb544fdd4a91b7f58330322ea31f', '19aa9a9e61e849ecb6ceb70b55de1e63'],
          status: 1,
          appApproveSetDTOS: null,
          openPostAuthorize: false,
          openAutoApprove: false,
          ruleWhereFilters: null,
          dynamicWhereFilters: null,
          selectCopyPostIds: [],
          addCopyPostIds: [],
          cancelCopyPostIds: [],
          hiddenInTransaction: false,
          belongDepartmentId: 'af8a8c80607842caa1ca9fa6b4cfddac',
          choiceType: 0,
          filterOutCorporations: 0,
          postTypes: [],
          mangeAuths: [],
          useGuest: null,
          openGuestAuth: null,
          guestUserIds: null,
          guestUserChanges: [],
          dataAuthorizationDetailDTOList: null,
          useType: 1,
        },
      ],
    };
    const approveDetailVO = data?.formAuthInfoVOS || [];
    const firstItem = approveDetailVO[0];
    setApproveInfoList(approveDetailVO);
    if (firstItem) {
      setCurrentApproveInfo(firstItem);
      setApproveIds([firstItem.appId]);
    }
  }, [approveId]);

  useEffect(() => {
    fetchApproveInfo();
  }, [fetchApproveInfo]);

  // 是否展示审批意见弹窗
  const [popVisible, setPopVisible] = useState(false);
  // 审批意见
  const [suggestion, setSuggestion] = useState('');

  // 审批 拒绝 | 通过
  const commit = (type: 'AGREE' | 'REJECT') => {
    if (approveIds.length === 0) {
      message.warning('请选择');
      return;
    }
    Modal.confirm({
      title: type === 'AGREE' ? `确定通过${approveIds.length}个表单的授权审批吗？` : `确定拒绝${approveIds.length}个表单的授权审批吗？`,
      centered: true,
      width: 340,
      onOk: async () => {
        // const params = { postId: bizId, confirmSuggestion: suggestion, approveStatus: type, planIds: approveIds };
        // if (type === 'AGREE') delete params['confirmSuggestion'];
        // await orgAPI.submitApprovePlan(params);
        // message.success('操作成功！');
        // if (approveInfoList.length > approveIds.length) {
        //   fetchApproveInfo();
        // } else {
        //   refreshWaitingList();
        //   bridge.close();
        // }
      },
    });
  };

  return (
    <LayoutPage className={styles.pageBox} header={{ title: '表单授权审批' }} footer={false} canDrag onCancel={() => bridge.close()}>
      <div className={styles.container}>
        <div className={styles.header}>
          <div>
            <InfoCircleOutlined style={{ color: '#4D7BF6', marginRight: 10 }} />
            {<span>{`当前为${currentApproveInfo.createPostName}—${currentApproveInfo.createUserName}发起的表单授权审批`}</span>}
          </div>
          <div className={styles.submitTime}>{formatDateTime(currentApproveInfo.submitTime)}</div>
        </div>

        <div className={styles.pageContent}>
          {/* 左侧部门列表 */}
          <div className={styles.left}>
            <div className={styles.formTotal}>表单列表</div>
            <Checkbox.Group
              style={{ width: '100%', height: 'calc(100% - 95px)', overflowX: 'hidden' }}
              value={approveIds}
              onChange={(checkedValues) => setApproveIds(checkedValues)}
            >
              {approveInfoList.map((approveInfo) => {
                const { appId, appName } = approveInfo || {};
                return (
                  <div
                    key={appId}
                    className={classNames(styles.formItem, {
                      [styles.active]: appId === currentApproveInfo.appId,
                    })}
                    onClick={() => {
                      setCurrentApproveInfo(approveInfo);
                    }}
                  >
                    <div className={styles.checkItem}>
                      <Checkbox value={appId} />
                      <div className={styles.appInfo}>
                        <div className={styles.headerInfo}>
                          <img src={`${CDN_URL_DATASOURCE}/<EMAIL>`} width={20} alt="" style={{ marginRight: 8 }} />
                          <span>{appName}</span>
                        </div>
                        <div className={styles.createUser}>
                          <TextEllipsisTooltip
                            tooltipStyle={{ zIndex: 9999 }}
                            text={`创建人信息：${approveInfo.createPostName}—${approveInfo.createUserName}`}
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                );
              })}
            </Checkbox.Group>
            <div className={styles.actions}>
              <Popover
                placement="topLeft"
                open={popVisible}
                rootClassName={styles.overlayPop}
                content={
                  <div className={styles.popContainer}>
                    <div className={styles.content}>
                      <div className={styles.contentLabel}>审批意见：</div>
                      <Input.TextArea
                        placeholder="请输入审批意见"
                        autoSize={{ minRows: 3 }}
                        value={suggestion}
                        maxLength={300}
                        onChange={(e) => setSuggestion(e.target.value)}
                      />
                    </div>
                    <div className={styles.buttons}>
                      <Button
                        onClick={() => {
                          setPopVisible(false);
                          setSuggestion('');
                        }}
                      >
                        取消
                      </Button>
                      <Button disabled={!suggestion} type="primary" onClick={() => commit('REJECT')}>
                        确认
                      </Button>
                    </div>
                  </div>
                }
                trigger="click"
              >
                <Button danger onClick={() => setPopVisible(true)}>
                  拒绝
                </Button>
              </Popover>
              <Button type="primary" onClick={() => commit('AGREE')}>
                通过
              </Button>
            </div>
          </div>

          {/* 右侧部门审批详情 */}
          <div key={currentApproveInfo.approveDetailVO?.approveId} className={styles.right}>
            {renderRightContent(currentApproveInfo)}
          </div>
        </div>
      </div>
    </LayoutPage>
  );
});

export default FormAuthorizationApprove;
