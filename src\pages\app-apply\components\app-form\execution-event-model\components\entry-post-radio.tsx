import { PostEntryType } from '@/pages/app-designer/components/right-panel/form-design/button-options/constants';
import { Form, Radio } from '@gwy/components-web';
import EntryPostAuto from './entry-post-auto';

interface IProps {
  form: any;
  onClose: () => void;
}

const EntryPostRadio = ({ form, onClose }: IProps) => {
  const postEntryType = Form.useWatch(['postEntryType'], form);

  return (
    <Form form={form} labelAlign="left">
      <Form.Item label="入职类型" name="postEntryType" initialValue={PostEntryType.SELECT_EXIST}>
        <Radio.Group>
          <Radio value={PostEntryType.SELECT_EXIST}>选择已有岗位</Radio>
          <Radio value={PostEntryType.AUTO_CREATE}>自动生成岗位</Radio>
        </Radio.Group>
      </Form.Item>
      <div style={{ color: '#86909C', marginBottom: 10 }}>
        提示：{postEntryType === PostEntryType.SELECT_EXIST ? '请选择人员的岗位类型' : '将会为该人员自动生成岗位入职，请选择要入职的部门'}
      </div>
      <EntryPostAuto isAuto={postEntryType === PostEntryType.AUTO_CREATE} />
    </Form>
  );
};

export default EntryPostRadio;
