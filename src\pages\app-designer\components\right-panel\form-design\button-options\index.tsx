import { ButtonOperateType, buttonOptions, ButtonStyle } from '@/const/button-config';
import { AppDesignerContext } from '@/pages/app-designer/context';
import { DeleteOutlined, PlusCircleOutlined } from '@ant-design/icons';
import { Button, Form, Radio, Select } from 'antd';
import { useContext, useEffect } from 'react';
import SectionHeader from '../../../section-header';
import ExecutionEvents from './components/execution-events';
import styles from './index.less';

export const Default_Submit_Buttons = [{ operateType: ButtonOperateType.Launch, buttonStyle: ButtonStyle.Primary }];
export const Default_Approve_Buttons = [
  { operateType: ButtonOperateType.Agree, buttonStyle: ButtonStyle.Primary },
  { operateType: ButtonOperateType.Refuse, buttonStyle: ButtonStyle.Danger },
  { operateType: ButtonOperateType.Return, buttonStyle: ButtonStyle.PrimaryGhost },
];

const ButtonOptions = () => {
  const { buttonConfigs, setButtonConfigs } = useContext(AppDesignerContext);

  const [form] = Form.useForm();
  const buttonList = Form.useWatch('buttonList', form);

  useEffect(() => {
    form.setFieldValue('buttonList', buttonConfigs);
  }, [buttonConfigs]);

  return (
    <div className={styles.container}>
      <SectionHeader title="表单按钮配置" />
      <Form
        form={form}
        layout="vertical"
        onValuesChange={async () => {
          setTimeout(async () => {
            const { buttonList } = await form.getFieldsValue();
            setButtonConfigs(buttonList);
          }, 0);
        }}
      >
        <Form.List name="buttonList">
          {(fields, { add, remove }) => {
            return (
              <div className={styles.listBox}>
                {fields.map(({ key, name }) => {
                  return (
                    <div key={key} className={styles.listItem}>
                      <div key={key} className={styles.contentBox}>
                        <Form.Item label="按钮名称" name={[name, 'operateType']} rules={[{ required: true, message: '请选择' }]}>
                          <Select
                            placeholder="请选择"
                            onChange={(v) => {
                              const btn = buttonOptions.find((b) => b.value === v);
                              form.setFieldValue(['buttonList', name, 'buttonStyle'], btn.defaultButtonStyle);
                            }}
                          >
                            {buttonOptions.map((item) => {
                              return (
                                <Select.Option
                                  key={item.value}
                                  value={item.value}
                                  disabled={
                                    buttonList?.some((b) => b.operateType === item.value) ||
                                    (buttonList?.length > 1 && item.value === ButtonOperateType.Launch)
                                  }
                                >
                                  {item.label}
                                </Select.Option>
                              );
                            })}
                          </Select>
                        </Form.Item>
                        <Form.Item label="按钮样式" name={[name, 'buttonStyle']} initialValue={'primary'}>
                          <Radio.Group>
                            <div style={{ display: 'flex', flexWrap: 'wrap', gap: 16 }}>
                              <Radio value={ButtonStyle.Primary}>
                                <Button type="primary">按钮</Button>
                              </Radio>
                              <Radio value={ButtonStyle.PrimaryGhost}>
                                <Button type="primary" ghost>
                                  按钮
                                </Button>
                              </Radio>
                              <Radio value={ButtonStyle.Danger}>
                                <Button danger>按钮</Button>
                              </Radio>
                              <Radio value={ButtonStyle.Default}>
                                <Button type="default">按钮</Button>
                              </Radio>
                            </div>
                          </Radio.Group>
                        </Form.Item>

                        <ExecutionEvents form={form} name={name} />
                      </div>

                      <DeleteOutlined className={styles.removeIcon} onClick={() => remove(name)} />
                    </div>
                  );
                })}

                {buttonList?.length < 4 && buttonList[0]?.operateType !== ButtonOperateType.Launch && (
                  <Button color="primary" variant="dashed" block icon={<PlusCircleOutlined />} onClick={() => add({})}>
                    添加按钮
                  </Button>
                )}
              </div>
            );
          }}
        </Form.List>
      </Form>
    </div>
  );
};

export default ButtonOptions;
