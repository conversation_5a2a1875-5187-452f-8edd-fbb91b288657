import { EditOutlined } from '@ant-design/icons';
import { Switch } from '@gwy/components-web';
import styles from './index.less';

interface IProps {
  value?: boolean;
  onChange?: (value: boolean) => void;
  onEdit: () => void;
}

const FormItemSwitch = ({ value, onChange, onEdit }: IProps) => {
  return (
    <div className={styles.container}>
      <Switch checked={value} onChange={onChange} />
      {value && (
        <span className={styles.edit} onClick={() => onEdit()}>
          <EditOutlined /> 配置条件
        </span>
      )}
    </div>
  );
};

export default FormItemSwitch;
