export enum ButtonOperateType {
  Launch = 'LAUNCH',
  Agree = 'AGREE',
  Refuse = 'REFUSE',
  Return = 'RETURN',
  End = 'END',
}

export enum ButtonStyle {
  Primary = 'primary',
  PrimaryGhost = 'primary-ghost',
  Danger = 'danger',
  Default = 'default',
}

export const buttonOptions = [
  { label: '发起', value: ButtonOperateType.Launch, defaultButtonStyle: ButtonStyle.Primary },
  { label: '同意', value: ButtonOperateType.Agree, defaultButtonStyle: ButtonStyle.Primary },
  { label: '拒绝', value: ButtonOperateType.Refuse, defaultButtonStyle: ButtonStyle.Danger },
  { label: '退回', value: ButtonOperateType.Return, defaultButtonStyle: ButtonStyle.PrimaryGhost },
  { label: '完结', value: ButtonOperateType.End, defaultButtonStyle: ButtonStyle.Default },
];
