.authorizationModal {
  font-size: 14px;
  color: #1d2129;

  :global {
    .ant-modal-body {
      height: 600px;
      overflow-y: auto;
    }

    .ant-modal-footer {
      padding: 8px 16px !important;
      box-shadow: 0 -1px 4px 0 rgba(0, 0, 0, 10%);
    }
  }

  .footer {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .doNotDisturbSection {
      .questionIcon {
        margin-left: 4px;
        color: #4e5969;
        cursor: pointer;
      }
    }

    .btnBox {
      :global {
        .ant-btn {
          width: 72px;
          height: 32px;
        }
      }

      button:first-child {
        margin-right: 16px;
      }
    }
  }

  .content {
    .section {
      display: flex;
    }

    .positionTypeSection {
      margin-bottom: 16px;
      border-bottom: 1px solid #e5e6eb;

      .departmentRow {
        display: flex;
        align-items: center;
        border-top: 1px solid #e5e6eb;
        background-color: #fff;

        :global {
          .ant-checkbox-wrapper {
            font-size: 14px;
            color: #1d2129;
            font-weight: 500;
          }
        }

        .departmentCheckbox {
          padding: 0 20px;
        }

        .positionTypes {
          border-left: 1px solid #e5e6eb;
          padding: 12px 19px;
          display: flex;
          flex-wrap: wrap;
          gap: 12px;
          flex: 1;

          :global {
            .ant-checkbox-wrapper {
              font-size: 12px;
            }
          }
        }
      }
    }

    .sectionTitle {
      display: flex;
      align-items: center;
      font-size: 14px;
      font-weight: bold;
      color: #1d2129;
    }

    .hintText {
      font-size: 12px;
      font-weight: 400;
      line-height: 20px;
      color: #86909c;
      margin: 8px 0 12px;
    }

    .specificPositionSection {
      margin-bottom: 16px;
      background: #fff;
      border-radius: 2px;

      .positionSelector {
        display: flex;
        height: 290px;
        border: 1px solid #e5e6eb;
        border-radius: 6px;
        overflow: hidden;

        .selectionPanel {
          padding: 12px;
          width: 280px;
          flex-shrink: 0;
          border-right: 1px solid #e5e6eb;
          display: flex;
          flex-direction: column;
          overflow: hidden;

          .departmentList {
            flex: 1;
            overflow-y: auto;
            padding: 8px 0;
            width: 100%;
            min-width: 0;

            :global {
              .ant-tree-switcher-icon {
                position: relative;
                top: 4px;
              }

              .ant-tree .ant-tree-switcher:not(.ant-tree-switcher-noop):hover::before {
                background-color: transparent;
              }

              .ant-tree-node-content-wrapper {
                min-width: 0;
                padding-inline: 0;
                width: 100%;
                overflow: hidden;
              }
            }

            .departmentHeader {
              display: flex;
              align-items: center;
              padding: 4px 0;
              cursor: pointer;
              transition: background-color 0.2s;
              min-width: 0;
              width: 100%;
              overflow: hidden;

              .departmentName {
                font-size: 14px;
                color: #1d2129;
                font-weight: 500;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                max-width: 100%;
                width: 100%;
                display: block;
              }
            }

            .positionItem {
              padding: 4px 0;
              margin: 0;
              width: 100%;
              min-width: 0;
              overflow: hidden;

              :global {
                .ant-checkbox-wrapper {
                  width: 100%;
                  align-items: flex-start;
                  min-width: 0;
                  overflow: hidden;

                  .ant-checkbox + * {
                    flex: 1;
                    min-width: 0;
                    overflow: hidden;
                  }
                }
              }

              .positionInfo {
                display: flex;
                align-items: center;
                gap: 8px;
                width: 100%;
                min-width: 0;
                overflow: hidden;

                .positionDetails {
                  flex: 1;
                  min-width: 0;
                  overflow: hidden;

                  .userName {
                    font-size: 14px;
                    color: #1d2129;
                    font-weight: 500;
                    line-height: 1.4;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    max-width: 100%;
                    width: 100%;
                    display: block;
                  }

                  .postName {
                    font-size: 12px;
                    color: #86909c;
                    font-weight: 400;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    max-width: 100%;
                    width: 100%;
                    display: block;
                  }
                }
              }
            }
          }
        }

        .selectedPanel {
          flex: 1;
          display: flex;
          flex-direction: column;
          overflow: hidden;

          .selectedTitle {
            padding: 12px 16px;
            font-size: 14px;
            font-weight: bold;
            color: #1d2129;
          }

          .selectedList {
            flex: 1;
            overflow-y: auto;
            width: 100%;
            min-width: 0;

            .emptyState {
              text-align: center;
              color: #86909c;
              font-size: 14px;
              margin-top: 40px;
            }

            .selectedPosition {
              display: flex;
              align-items: center;
              justify-content: space-between;
              padding: 12px;
              width: 100%;
              min-width: 0;

              .positionInfo {
                display: flex;
                align-items: center;
                gap: 8px;
                flex: 1;
                max-width: calc(100% - 25px);
                width: 100%;
                min-width: 0;

                .positionDetails {
                  flex: 1;
                  min-width: 0;

                  .userName {
                    font-size: 14px;
                    color: #1d2129;
                    font-weight: 500;
                    line-height: 1.4;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    max-width: 100%;
                  }

                  .postName {
                    font-size: 12px;
                    color: #86909c;
                    line-height: 1.4;
                    margin-top: 2px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    max-width: 100%;
                  }
                }
              }

              .removeButton {
                cursor: pointer;
                color: #c9cdd4;
              }
            }
          }
        }
      }
    }
  }
}
