import { useBridge } from '@/hooks';
import { AppType } from '@/pages/app-designer/const';
import { getSubDataUnitConfigByVersionId } from '@/services/app-sub';
import { queryMutData } from '@/services/datasource-data';
import { getOrgDataUnitsTags } from '@/services/org-data-unit';
import { Post } from '@/types/post';
import { DataUnitTagVO } from '@/types/tag';
import { getMetaTagUniqueId } from '@/utils/metadata';
import { LayoutOutlined, PrinterOutlined } from '@ant-design/icons';
import { Button, Header, Input, Select, Space, TablePaginationConfig } from '@gwy/components-web';
import classNames from 'classnames';
import { isArray, isEmpty, pick } from 'lodash-es';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { openAppApply } from '../../index';
import DataList from './data-list';
import styles from './index.less';

type Props = {
  formId?: number;
  formVersionId?: number;
  post?: Post;
  isPreview?: boolean;
  formVersion?: any;
  setFilterOpen: (vlaue: boolean) => void;
  cTagsMap?: any;
  dynamicConditionMap?: any;
  filters?: any;
  [key: string]: any;
};

export enum DATA_TYPE_ENUMS {
  Main_Data = 1,
  Child_Data = 2,
}

const AppTable = (props: Props) => {
  const bridge = useBridge();
  const { formId, formVersionId, post, isPreview, formVersion, setFilterOpen, cTagsMap, dynamicConditionMap, filters, noGetList } = props;
  // setFilterOpen(true)
  console.log(setFilterOpen, filters, 'setFilterOpen-----');
  // 表单版本信息
  // const [formVersion, setFormVersion] = useState<FormVO>();
  // 表单字段
  const formFields = useMemo(() => {
    return formVersion?.extConfig?.fields || [];
  }, [formVersion]);
  // 标签映射
  // const [cTagsMap, setCTagsMap] = useState<Record<number, DataUnitTagVO>>({});
  // 数据源
  const [dataSource, setDataSource] = useState<any[]>([]);
  const [pagination, setPagination] = useState<TablePaginationConfig>({
    current: 1,
    pageSize: 10,
  });
  const [total, setTotal] = useState(0);
  // 已读未读
  const [isReadVal, setIsReadVal] = useState(1);
  // const [filterOpen, setFilterOpen] = useState(false);
  const [sortOpen, setSortOpen] = useState(false);
  const [filterData, setFilterData] = useState<any>({
    total: 0,
    List: [],
  });
  const [tableHasColumns, setTablehasColumns] = useState([]);
  // 筛选条件
  const [newConditionGroups, setNewConditionGroups] = useState(null);
  // 获取标签
  const [mainSetting, setMainSetting] = useState<{
    [key: string]: any;
  }>({});
  // 标签映射
  const [dataGroupTagsMap, setDataGroupTagsMap] = useState<Record<number, DataUnitTagVO>>({});
  // 获取标签

  // 是否有配置数据聚合
  const hasConfigDataGroup = useMemo(() => {
    const hasConfigDataGroup = formVersion?.dataUnits[0]?.extConfig?.subDataUnitIds?.length > 0;
    console.log(hasConfigDataGroup, 'hasConfigDataGroup---------');
    return hasConfigDataGroup;
  }, [formVersion]);
  const fetchDataUnitsWithTags = async (dataUnitIds) => {
    const dataUnits = await getOrgDataUnitsTags({ orgId: post?.orgId, dataUnitIds });
    setDataGroupTagsMap(
      dataUnits?.reduce((acc, cur) => {
        cur.tagList?.forEach((tag) => {
          acc[getMetaTagUniqueId(tag)] = tag;
        });
        return acc;
      }, {} as Record<number, DataUnitTagVO>),
    );
  };
  // 获取主应用的配置
  const findDataGroupVersion = async (formVersionId, dataUnitId) => {
    const data = await getSubDataUnitConfigByVersionId({
      dataUnitId,
      formVersionId,
    });
    setMainSetting(data);
    fetchDataUnitsWithTags([data?.dataUnitId]);
  };
  // 数据聚合主应用标签
  const dataGroupMainTags = useMemo(() => {
    console.log(mainSetting?.fields, 'fields------------');
    return mainSetting?.extConfig?.fields || [];
  }, [mainSetting]);

  useEffect(() => {
    if (hasConfigDataGroup && !isEmpty(formVersion)) {
      const formVersionId = formVersion?.formVersionId;
      const dataUnitId = formVersion?.dataUnits?.[0]?.dataUnitId;
      findDataGroupVersion(formVersionId, dataUnitId);
    }
  }, [formVersion, hasConfigDataGroup]);

  // const fetchDataUnitsWithTags = async (dataUnitIds) => {
  //   const dataUnits = await getOrgDataUnitsTags({ orgId: post?.orgId, dataUnitIds });
  //   setCTagsMap(
  //     dataUnits?.reduce((acc, cur) => {
  //       cur.tagList?.forEach((tag) => {
  //         acc[getMetaTagUniqueId(tag)] = tag;
  //       });
  //       return acc;
  //     }, {} as Record<number, DataUnitTagVO>),
  //   );
  // };

  // 获取表单版本信息
  // const fetchFormVersion = async () => {
  //   const formVersion = await getFormVersion(formVersionId);
  //   setFormVersion(formVersion);

  //   fetchDataUnitsWithTags(formVersion.dataUnits.map((item) => item.dataUnitId));
  //   return formVersion;
  // };

  useEffect(() => {
    setPagination((pre) => ({
      ...pre,
      current: 1,
    }));
  }, [filters]);

  // 获取数据
  const fetchDataSource = async (formVersion: any) => {
    const { conditionGroups: dynamicParamsConfition, extConfig } = filters || {};
    // console.log(filters, 'filters-----------')
    // const params = {
    //   formVersionId: isPreview ? null : formVersionId,
    //   postId: post?.postId,
    //   dataUnitId: formVersion?.dataUnits?.[0]?.dataUnitId,
    //   extConfig: isPreview ? null : !isEmpty(dynamicConditionMap) ? extConfig : null,
    //   conditionGroups: isPreview ? formVersion?.dataUnits?.[0]?.conditionGroups : !isEmpty(dynamicConditionMap) ? dynamicParamsConfition : null,
    //   currentPage: pagination.current,
    //   pageSize: pagination.pageSize,
    // };
    let allDataUnits = formVersion?.dataUnits;
    // if (!isEmpty(dynamicParamsConfition)) {
    allDataUnits = allDataUnits?.map((item) => {
      if (item.dataUnitId === filters?.dataUnitId) {
        return {
          ...item,
          ...filters,
        };
      }
      return item;
    });
    // }
    const params = {
      formVersionId,
      postId: post?.postId,
      pageSize: pagination.pageSize,
      currentPage: pagination.current,
      queryConfig: {
        dataUnits: allDataUnits,
        queryFormType: 1, // 1=基础分组，2=数据表格
      },
    };
    const { records, total } = await queryMutData(params);
    setDataSource(records);
    setTotal(total);
    setFilterData({
      total: total,
      list: records,
    });
  };
  useEffect(() => {
    if (!isEmpty(formVersion)) {
      // fetchDataUnitsWithTags(formVersion.dataUnits.map((item) => item.dataUnitId));
    }
  }, [formVersion]);

  // useEffect(() => {
  //   (async () => {
  //     await fetchFormVersion();
  //   })();
  // }, []);

  useEffect(() => {
    if (noGetList) {
      return;
    }
    if (!formVersion) return;
    fetchDataSource(formVersion);
  }, [formVersion, pagination, dynamicConditionMap, filters, noGetList]);

  // 已查看过的数据ID列表
  const [readIds, setReadIds] = useState<any[]>([]);
  useEffect(() => {
    const ids = window.localStorage.getItem(`read_${formVersionId}`);
    ids && setReadIds(ids.split(','));
  }, []);

  const renderColumns = useCallback(
    (columns) => {
      // setTablehasColumns(columns)
      return [
        ...columns,
        {
          title: '操作',
          dataIndex: 'actions',
          render: (value, r) => {
            const isMainData = r?.data_type === DATA_TYPE_ENUMS.Main_Data;
            const ischildData = r?.data_type === DATA_TYPE_ENUMS.Child_Data;
            const isSameGroup = !!r.split_type || isMainData || ischildData; // 同组数据----组合标签，以及数据拆分
            const hasChildren = r?.children?.length > 0;
            // && hasChildren
            const isSamePiece = !isSameGroup; // 同条数据 同条数据和同组数据互斥

            const sameSubChildren = r.same_sub_data === 1;

            const { main_data_id } = r || {};

            // const allDataIds = r
            const btn = (
              <a
                onClick={() => {
                  setReadIds((pre) => {
                    const newIds = [...pre, r._id];
                    window.localStorage.setItem(`read_${formVersionId}`, newIds.join(','));
                    return newIds;
                  });
                  let allDataIds = [];
                  let parentRecord = {} as any;
                  const searchList = filterData.list;
                  if (main_data_id) {
                    parentRecord = searchList?.find((item) => item._id === main_data_id);
                    if (!parentRecord) {
                      allDataIds = [main_data_id];
                    }
                    console.log(parentRecord, searchList, main_data_id, allDataIds, 'data-------');
                  } else {
                    parentRecord = searchList?.filter((record) => record._id === r?._id)?.[0];
                  }
                  if (parentRecord?.children?.length > 0) {
                    const childIds = (parentRecord?.children || [])?.map((item) => item?._id);
                    allDataIds = Array.from(new Set([...allDataIds, parentRecord?._id, ...childIds]));
                  }
                  let dataGroupOption;
                  if (hasConfigDataGroup) {
                    let dataUnitId = formVersion?.dataUnits?.[0]?.dataUnitId;
                    const subDataUnitIds = formVersion?.dataUnits?.[0]?.extConfig?.subDataUnitIds || [];
                    const { sub_data_ids } = r;
                    const subDataUnitId = subDataUnitIds?.find((subId) => subId === r.source_data_unit_id);
                    if (subDataUnitId) {
                      dataUnitId = subDataUnitId;
                    }
                    dataGroupOption = {
                      dataId: r._id,
                      formVersionId: formVersion?.formVersionId,
                      dataUnitId,
                    };
                  }
                  openAppApply(bridge, {
                    pathParams: '?p=2',
                    state: {
                      isPreview,
                      app: {
                        appType: AppType.SUBMIT,
                        formId,
                        formVersionId,
                      },
                      post,
                      dataId: r._id,
                      isSameGroup: isMainData || ischildData,
                      isSamePiece: isSamePiece && hasChildren && !sameSubChildren && !ischildData && !isMainData,
                      allDataIds,
                      filters,
                      dataGroupOption,
                    },
                  });
                }}
              >
                查看
              </a>
            );
            console.log(r, 'r-----------');
            // 如果是组合标签数据，以及数据拆分及打上了主子数据
            if ((isMainData || ischildData) && !isMainData) {
              return btn;
              // 已被拆分，此时主数据无查看，子数据可查看
            }
            // 由同条数据产生的的数据，只有最新一条操作记录产生的数据可查看
            if (isSamePiece && !sameSubChildren) {
              return btn;
            }
            if (!isSameGroup && !isSamePiece) {
              return btn;
            }
            // 如果是正常单行数据，正常查看
            // if (!r.group_tag_set) {
            //   if (r.rowSplitNoQuery) {
            //     return null;
            //   } else {
            //     return btn;
            //   }
            // }
            return null;
          },
        },
      ];
    },
    [formVersion, filterData, filters, hasConfigDataGroup],
  );
  const handleSearch = (value) => {
    let arr = [...dataSource];
    if (isEmpty(value)) {
      setFilterData({ total: dataSource.length, list: arr });
      return;
    }
    const dataIndexList = tableHasColumns.map((col) => col.dataIndex);
    const serchFn = (arr) => {
      const data = arr.filter((item: any) => {
        const itemValues = Object.values(pick(item || {}, dataIndexList)) || [];
        console.log('itemValues---------', itemValues, dataIndexList);
        let flag = false;
        for (let index = 0; index < itemValues.length; index++) {
          const element = itemValues[index];
          let text: any = '';
          if (typeof element === 'string' || typeof element === 'number') {
            text = String(element);
          }
          if (isArray(element)) {
            const elementValue = element?.[0];
            try {
              const jsonList = JSON.parse(elementValue);
              if (isArray(jsonList)) {
                text = JSON.stringify(jsonList.map((ele) => ele.fileName || ele.fileUrl || ele.templateName || ele.templateUrl));
              } else if (typeof elementValue === 'string' || typeof elementValue === 'number') {
                text = String(elementValue);
              }
            } catch (e) {
              if (typeof elementValue === 'string' || typeof elementValue === 'number') {
                text = String(elementValue);
              }
            }
          }
          // 待数据结构清晰后在处理主子数据筛选
          //  if (item?.children?.length > 0) {
          //   const results = serchFn(item.children)
          //   if (results?.length > 0) {
          //     item.children = results;
          //     flag = true;
          //     break;
          //   }
          // }
          if (element instanceof Object && !isEmpty(element) && !item?.children?.length) {
            console.log('element', element);
            text = typeof element?.value === 'number' || typeof element?.value === 'string' ? String(element?.value) : '';
          }

          if (text?.includes(value)) {
            flag = true;
            break;
          }
        }
        return flag;
      });
      return data;
    };
    const data = serchFn(arr);
    setFilterData({ total: data.length, list: data });
  };

  return (
    <div className={classNames(styles.container, styles.printElement)}>
      <div className={styles.header}>
        <Header
          title="表单列表"
          closable
          onClose={() => {
            bridge.close();
          }}
        />
      </div>
      <div className={classNames(styles.printElement, styles.content)} id="print-iframe-id">
        <DataList
          isPreview={isPreview}
          formFields={hasConfigDataGroup ? dataGroupMainTags : formFields}
          cTagsMap={hasConfigDataGroup ? dataGroupTagsMap : cTagsMap}
          dataSource={filterData.list}
          toolBarRender={() => [
            // !isPreview && (
            <div className={styles.containerHeader} key="custom-toolbar">
              <Space>
                <Input.Search
                  maxLength={20}
                  allowClear
                  placeholder="请输入关键词进行搜索"
                  style={{ width: 250 }}
                  onSearch={(value) => {
                    handleSearch(value);
                  }}
                />
                <Select
                  style={{ width: '100px' }}
                  value={isReadVal}
                  options={[
                    { value: 1, label: '全部数据' },
                    { value: 2, label: '已读' },
                    { value: 3, label: '未读' },
                  ]}
                  onChange={(e) => {
                    setIsReadVal(e);
                    let arr = [...dataSource];
                    if (e === 1) return setFilterData({ total: arr.length, list: arr });
                    if (e === 2) arr = arr.filter((i) => readIds.includes(i._id));
                    if (e === 3) arr = arr.filter((i) => !readIds.includes(i._id));
                    setFilterData({ total: arr.length, list: arr });
                  }}
                />
                <Button
                  type="default"
                  onClick={() => {
                    setFilterOpen(true);
                  }}
                  icon={<LayoutOutlined />}
                >
                  数据查询
                </Button>
                {/* <Popover
                  zIndex={1000}
                  destroyTooltipOnHide
                  open={filterOpen}
                  placement="bottomRight"
                  trigger="click"
                  overlayClassName={styles.noPaddingPopover}
                  onOpenChange={(open) => setFilterOpen(open)}
                  content={
                    <>筛选条件</>
                    // <FilterConditions
                    //   mainRuleTags={mainRuleTags}
                    //   tags={isPreview ? tags : whereFilterOptions || tags}
                    //   oldWhereFilters={oldWhereFilters}
                    //   newWhereFilters={newWhereFilters}
                    //   groupSetDTO={groupSetDTO}
                    //   setNewWhereFilters={setNewWhereFilters}
                    //   setState={setState}
                    //   setFilterOpen={setFilterOpen}
                    //   initData={initData}
                    // />
                  }
                >
                 
                </Popover> */}
                {/* {!isPreview && (
                  <Popover
                    destroyTooltipOnHide
                    open={sortOpen}
                    placement="bottomRight"
                    trigger="click"
                    overlayClassName={styles.noPaddingPopover}
                    onOpenChange={(open) => setSortOpen(open)}
                    content={
                      <SortRules
                        state={state}
                        userId={userId}
                        tags={tags}
                        setSortOpen={setSortOpen}
                        sortRules={sortRules}
                        setSortRules={setSortRules}
                        initData={initData}
                      />
                    }
                  >
                    <Button type="default" icon={<OrderedListOutlined />}>
                      排序规则
                    </Button>
                  </Popover>
                )} */}
              </Space>
            </div>,
            // )
            <Button onClick={() => window.print()} type="default" icon={<PrinterOutlined />} key="print-key">
              打印
            </Button>,
          ]}
          renderColumns={renderColumns}
          dataUnits={formVersion?.dataUnits}
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: filterData.total,
            onChange: (page, pageSize) => {
              setPagination((pre) => ({ ...pre, current: page, pageSize }));
            },
          }}
          readIds={readIds}
          setTablehasColumns={setTablehasColumns}
        />
      </div>
      {/* {filterOpen && <FilterGroup canRelateTags={[]} tags={[]} />} */}
    </div>
  );
};

export default AppTable;
