import { FormField } from '@/types/form-field';
import { DataUnitTagVO } from '@/types/tag';
import { memo } from 'react';
import { UseIn } from '../const';
import ObjectItem from './object-item';

export type ObjectMetaProps = {
  useIn?: UseIn;
  tag?: DataUnitTagVO;
  field?: FormField;
  value?: any;
  onChange?: (value) => void;
  isPreview?: boolean;
};

const ObjectMeta = memo(({ useIn, tag, field, value, onChange, isPreview }: ObjectMetaProps) => {
  if (field?.readonly || isPreview) {
    if (useIn === UseIn.Designer) {
      return '回显数据';
    }
    if (Array.isArray(value)) {
      return value?.map((item) => item.value)?.join('、') ?? '-';
    }
    const { value: tagValue, data: tagData } = value || {};
    return tagData?.map((item) => item.value)?.join('、') ?? '-';
  }

  return <ObjectItem useIn={useIn} tag={tag} field={field} value={value} onChange={onChange} />;
});

export default ObjectMeta;
