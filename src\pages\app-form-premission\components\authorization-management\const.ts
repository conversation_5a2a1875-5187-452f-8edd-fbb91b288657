// 岗位类型 - 运营部门
export const operationPositionTypes = [
  { id: '1', name: '全职岗', checked: false },
  { id: '2', name: '副职岗', checked: false },
  { id: '3', name: '人事岗', checked: false },
  { id: '4', name: '主职岗', checked: false },
  { id: '5', name: '兼职岗', checked: false },
  { id: '6', name: '关联岗', checked: false },
  { id: '7', name: '顶位岗', checked: false },
];

// 岗位类型 - 附建组织
export const attachedOrgPositionTypes = [
  { id: '8', name: '全职岗', checked: false },
  { id: '9', name: '副职岗', checked: false },
  { id: '10', name: '组织岗', checked: false },
  { id: '11', name: '附主岗', checked: false },
  { id: '12', name: '兼职岗', checked: false },
  { id: '13', name: '关联岗', checked: false },
  { id: '14', name: '顶位岗', checked: false },
];

// 管理权限
export const managePermissions = [
  { id: '15', name: '管理岗', checked: true, tooltipMsg: '针对企业/接管集团所有部门管理岗' },
  { id: '16', name: '执行岗', checked: true, tooltipMsg: '包括管理执行岗和执行岗' },
  { id: '17', name: '法人不可用', checked: false, tooltipMsg: '法人岗不展示当前表单' },
];

// 部门数据
export const departments = [
  {
    id: '1',
    name: '运营部门',
    checked: false,
    positionTypes: operationPositionTypes,
  },
  {
    id: '2',
    name: '附建组织',
    checked: false,
    positionTypes: attachedOrgPositionTypes,
  },
  {
    id: '3',
    name: '管理权限',
    checked: false,
    positionTypes: managePermissions,
  },
];

// 岗位选择模拟数据
export const positionSelectionData = [
  {
    departmentName: '3331',
    departmentId: 'af8a8c80607842caa1ca9fa6b4cfddac',
    children: [
      {
        departmentName: '妇女联合',
        departmentId: 'a16a05d3626c4d908b1df84ba871c47d',
        children: [],
        posts: [
          {
            postId: 'eeb9430bae804280989537031b2c1dae',
            orgId: '65af38ce65220500072055de',
            orgName: '测试大集团',
            postName: '管理岗助理执行岗',
            userName: '张三',
            avatar: 'https://gw.alipayobjects.com/zos/rmsportal/BiazfanxmamNRoxxVxka.png',
          },
        ],
      },
      {
        departmentName: '基金会',
        departmentId: 'b2a23d2e8b05482f8f82966014073de6',
        children: [],
        posts: [
          {
            postId: 'f1b9430bae804280989537031b2c1dae',
            orgId: '65af38ce65220500072055de',
            orgName: '测试大集团',
            postName: '管理员2',
            userName: '李四',
            avatar: 'https://gw.alipayobjects.com/zos/rmsportal/BiazfanxmamNRoxxVxka.png',
          },
        ],
      },
      {
        departmentName: '知联会',
        departmentId: 'd951d1495a3c49dd93422aa6c088a32b',
        children: [],
        posts: [
          {
            postId: 'g2b9430bae804280989537031b2c1dae',
            orgId: '65af38ce65220500072055de',
            orgName: '测试大集团',
            postName: '法人',
            userName: '王五',
            avatar: 'https://gw.alipayobjects.com/zos/rmsportal/BiazfanxmamNRoxxVxka.png',
          },
        ],
      },
      {
        departmentName: '建友咨询',
        departmentId: 'd510e25a8f4743edae9bc96c2c84438f',
        children: [],
        posts: [
          {
            postId: 'h3b9430bae804280989537031b2c1dae',
            orgId: '65af38ce65220500072055de',
            orgName: '测试大集团',
            postName: '技术总监',
            userName: '赵六',
            avatar: 'https://gw.alipayobjects.com/zos/rmsportal/BiazfanxmamNRoxxVxka.png',
          },
        ],
      },
    ],
    posts: [
      {
        postId: '124952f02990433e986d9c68beeeedb8',
        orgId: '65af38ce65220500072055de',
        orgName: '测试大集团',
        postName: '部门经理',
        userName: '孙七',
        avatar: 'https://gw.alipayobjects.com/zos/rmsportal/BiazfanxmamNRoxxVxka.png',
      },
    ],
  },
];
