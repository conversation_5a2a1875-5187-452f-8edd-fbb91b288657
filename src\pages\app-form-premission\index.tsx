import { CalculatorOutlined, MenuFoldOutlined, PlusOutlined, SearchOutlined } from '@ant-design/icons';

import { useBridge } from '@/hooks';
import { formManageApi } from '@/services';
import { createFormVersion } from '@/services/app';
import { getDataUnitAndTag } from '@/services/datasource';
import { Post } from '@/types/post';
import { genOpenMicroApp } from '@/utils';
import { Button, Col, Empty, Input, LayoutPage, Row, Select, Tooltip } from '@gwy/components-web';
import { Bridge } from '@gwy/libs-web';
import { cloneDeep } from 'lodash-es';
import { useEffect, useMemo, useRef, useState } from 'react';
import { openAppDesigner } from '../app-designer';
import FieldsLibsModal from '../app-designer/components/left-panel/components-design/fields-config/fields-libs-modal';
import { AppType } from '../app-designer/const';
import PostListIndex from '../app-form-manage/components/post-list-index';
import CheckConfigModal from './components/check-config-modal';
import CardItem from './components/manage-list/card';
import { TABS_LIST } from './const';
import styled from './index.less';

export const refreshAppFormPremission = (bridge: Bridge) => {
  bridge.refresh('/datasource/app-form-premission');
};

export const openAppFormPremission = genOpenMicroApp('app-form-premission');

const tabops = [{ value: TABS_LIST.create, label: '我创建的' }];

const STATUS_TYPE = [
  {
    label: '全部',
    value: 'ALL',
  },
  {
    label: '启用中',
    value: 'true',
  },
  {
    label: '禁用中',
    value: 'false',
  },
];

const AppFormPremission = () => {
  const bridge = useBridge({
    onRefresh: () => {
      localRef.current.fetchFolderAppList();
    },
  });

  const localRef = useRef({
    fetchFolderAppList: () => {},
  });

  const [baseInfo, setBaseInfo] = useState<{
    [key: string]: any;
    postId: string;
    selectedPost: Post;
  }>(null);
  const postListRef = useRef(null);
  const [loading, setLoading] = useState(false);
  const [leftExpand, setLeftExpand] = useState<boolean>(true);
  const [searchPostInput, setSearchPostInput] = useState('');

  const [currentTabFolderApps, setCurrentTabFolderApps] = useState<any[]>([]);

  const [filterParams, setFilterParams] = useState<{
    status: string;
    appName: string;
    nowTab: TABS_LIST;
  }>({
    status: 'ALL',
    appName: '',
    nowTab: TABS_LIST.create,
  });

  const [fieldConfigModal, setFieldConfigModal] = useState<{
    open?: boolean;
  }>({
    open: false,
  });
  const [checkConfigModal, setCheckConfigModal] = useState<{
    open?: boolean;
    formData: any;
  }>({
    open: false,
    formData: null,
  });
  const [allDataUnitsWithTags, setAllDataUnitsWithTags] = useState<any[]>([]);
  const fetchDataUnitAndTag = async () => {
    const data = await getDataUnitAndTag({ postId: baseInfo?.postId });
    setAllDataUnitsWithTags(data);
  };

  const fetchFolderAppList = async () => {
    setLoading(true);
    try {
      const data = await formManageApi.getFormPermissionLists({ postId: baseInfo?.postId, name: filterParams.appName });
      setCurrentTabFolderApps(data || []);
    } catch (error) {}
    setLoading(false);
  };
  localRef.current.fetchFolderAppList = fetchFolderAppList;

  useEffect(() => {
    baseInfo?.postId && fetchFolderAppList();
  }, [filterParams.appName, baseInfo?.postId]);

  const renderFolderAndAppLists = useMemo(() => {
    let filterArr = cloneDeep(currentTabFolderApps);
    if (!filterParams.appName) {
      filterArr = currentTabFolderApps;
    } else {
      filterArr = currentTabFolderApps.filter((item) => item.name.includes(filterParams.appName));
    }
    if (['true', 'false'].includes(filterParams.status)) {
      filterArr = filterArr.slice(0).filter((item) => (item?.enable || 'false').toString() === filterParams.status);
    }
    return filterArr;
  }, [currentTabFolderApps, filterParams.appName, filterParams.status]);

  return (
    <LayoutPage
      loading={loading}
      footer={false}
      header={{ title: '表单管理', onClose: () => bridge.close() }}
      contentClassName={styled.layoutContent}
    >
      <div className={styled.container} id="onlyoffice-application-form-auth-list-container">
        <div className={styled.mainWrapper}>
          {leftExpand ? (
            <div className={styled.leftWrapper}>
              <div className={styled.titleWrapper}>
                <Tooltip title="收起岗位列表" placement="right">
                  <MenuFoldOutlined className={styled.menuFold} onClick={() => setLeftExpand(false)} />
                </Tooltip>
                {/* 岗位列表 */}
                <Input
                  prefix={<SearchOutlined style={{ color: '#909296' }} />}
                  style={{ width: 240 }}
                  placeholder={`搜索岗位名称`}
                  allowClear
                  onBlur={(e) => {
                    setSearchPostInput(e.target.value);
                  }}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                      setSearchPostInput((e?.target as HTMLInputElement)?.value.trim());
                    }
                  }}
                />
              </div>
              <PostListIndex ref={postListRef} setBaseInfo={setBaseInfo} searchInput={searchPostInput} />
            </div>
          ) : (
            <div className={styled.leftWrapperFold}>
              <Tooltip title="展开岗位列表" placement="right">
                <MenuFoldOutlined className={styled.menuExpand} onClick={() => setLeftExpand(true)} />
              </Tooltip>
            </div>
          )}

          <div className={styled.rightWrapper}>
            <div className={styled.contentWrapper}>
              <div className={styled.container}>
                <div className={styled.headerWrapper}>
                  <div className={styled.leftBox}>
                    <Row style={{ width: '100%' }}>
                      <Col span={17} style={{ flexWrap: 'nowrap', display: 'flex' }}>
                        <Input
                          style={{ width: 240, marginRight: 10 }}
                          prefix={<SearchOutlined style={{ color: '#909296' }} />}
                          placeholder={`请输入表单名称`}
                          allowClear
                          onBlur={(e) => {
                            setFilterParams({ ...filterParams, appName: e?.target?.value.trim() });
                          }}
                          onKeyDown={(e) => {
                            if (e.key === 'Enter') {
                              setFilterParams({ ...filterParams, appName: (e?.target as HTMLInputElement)?.value.trim() });
                            }
                          }}
                        />
                        <Select
                          value={filterParams.nowTab}
                          style={{ width: 'auto', minWidth: 85, maxWidth: 150, marginRight: 10 }}
                          options={tabops}
                          defaultValue={TABS_LIST.create}
                          popupMatchSelectWidth={140}
                          onChange={(val) => {
                            setFilterParams((pre) => ({ ...pre, nowTab: val }));
                          }}
                        />
                        <Select
                          style={{ width: 'auto', minWidth: 85, maxWidth: 150, marginRight: 10 }}
                          options={STATUS_TYPE}
                          defaultValue={'ALL'}
                          onChange={(val) => setFilterParams({ ...filterParams, status: val })}
                        />
                      </Col>
                      <Col span={7} style={{ flexWrap: 'nowrap', display: 'flex', justifyContent: 'flex-end' }}>
                        <Button
                          icon={<CalculatorOutlined />}
                          style={{
                            marginRight: 10,
                          }}
                          onClick={() => {
                            fetchDataUnitAndTag();
                            setFieldConfigModal({ open: true });
                          }}
                        >
                          字段库
                        </Button>
                        <Button
                          type="primary"
                          icon={<PlusOutlined />}
                          onClick={async () => {
                            try {
                              const formVersionId = await createFormVersion({
                                postId: baseInfo?.selectedPost?.postId,
                                appType: AppType.SUBMIT,
                              });
                              openAppDesigner(bridge, {
                                hideInMenu: true,
                                state: {
                                  formVersionId,
                                  post: baseInfo?.selectedPost,
                                },
                              });
                            } catch (error) {}
                          }}
                        >
                          新增表单
                        </Button>
                      </Col>
                    </Row>
                  </div>
                </div>

                <div className={styled.content}>
                  {renderFolderAndAppLists.length > 0 ? (
                    <CardItem
                      folderAppList={renderFolderAndAppLists}
                      fetchFolderAppList={fetchFolderAppList}
                      baseInfo={baseInfo}
                      setCheckConfigModal={setCheckConfigModal}
                    />
                  ) : (
                    <div
                      style={{
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center',
                        height: '100%',
                      }}
                    >
                      {!loading && <Empty description="暂无表单"></Empty>}
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {fieldConfigModal.open && (
        <FieldsLibsModal
          hideUse
          post={baseInfo?.selectedPost}
          configuredDataUnits={[]}
          allDataUnitsWithTags={allDataUnitsWithTags}
          onCancel={() => {
            setFieldConfigModal({ open: false });
          }}
          onOk={async (fieldIds) => {
            setFieldConfigModal({ open: false });
          }}
        />
      )}

      {checkConfigModal.open && (
        <CheckConfigModal
          post={baseInfo?.selectedPost}
          formData={checkConfigModal.formData}
          onOk={() => {
            setCheckConfigModal((prev) => ({ ...prev, open: false }));
          }}
          onCancel={() => {
            setCheckConfigModal((prev) => ({ ...prev, open: false }));
          }}
        />
      )}
    </LayoutPage>
  );
};

export default AppFormPremission;
