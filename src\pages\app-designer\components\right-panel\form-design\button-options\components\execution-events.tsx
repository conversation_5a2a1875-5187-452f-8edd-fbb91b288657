import { DeleteOutlined, PlusCircleOutlined } from '@ant-design/icons';
import { Button, ConfigProvider, Dropdown, Form } from 'antd';
import { useMemo } from 'react';
import { entryExclusionEvents, ExecutionEventType } from '../constants';
import DataSyncConfig from './data-sync-config';
import EventParams from './event-params';
import styles from './index.less';

interface IProps {
  form: any;
  name: number;
}

const ExecutionEvents = ({ form, name }: IProps) => {
  const executionEvents = Form.useWatch(['buttonList', name, 'events'], form);

  const menuItems = useMemo(() => {
    let menuItems = [
      {
        key: ExecutionEventType.POST_ENTRY,
        label: '入职',
      },
      {
        key: ExecutionEventType.POST_LEVEL,
        label: '离职',
      },
      {
        key: ExecutionEventType.POST_INVITE,
        label: '入职邀请',
      },
      {
        key: ExecutionEventType.DATA_SYNC,
        label: '数据同步',
      },
    ].filter(Boolean);
    menuItems = menuItems.map((item) => {
      if (entryExclusionEvents.includes(item.key)) {
        return {
          ...item,
          disabled: (executionEvents || []).some((item) => entryExclusionEvents.includes(item.eventType)),
        };
      }
      return {
        ...item,
        disabled: (executionEvents || []).some((i) => i.eventType === item.key),
      };
    });
    return menuItems;
  }, [executionEvents]);

  return (
    <Form.List name={[name, 'events']}>
      {(fields, { add, remove }) => {
        return (
          <>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 4 }}>
              <span>执行事件</span>
              <Dropdown
                trigger={['click']}
                menu={{
                  items: menuItems,
                  onClick: ({ key }) => {
                    add({ eventType: key, params: [] });
                  },
                }}
              >
                <Button size="small" type="link" icon={<PlusCircleOutlined />}>
                  添加事件
                </Button>
              </Dropdown>
            </div>
            {fields.map((field) => {
              const { eventType } = (executionEvents || [])?.[field.name] || {};
              const item = menuItems.find((item) => item.key === eventType);

              return (
                <div className={styles.executionEventItem} key={field.key}>
                  <Form.Item hidden name={[field.name, 'eventType']} />
                  {item && (
                    <div style={{ flexGrow: 1 }}>
                      {[ExecutionEventType.POST_ENTRY, ExecutionEventType.POST_LEVEL, ExecutionEventType.POST_INVITE].includes(item.key) && (
                        <ConfigProvider theme={{ token: { fontSize: 14 } }}>
                          <Form.Item name={[field.name, 'postEntryOrLevelConfig']} style={{ marginBottom: 0 }}>
                            <EventParams buttonIndex={name} eventIndex={field.name} item={item} />
                          </Form.Item>
                        </ConfigProvider>
                      )}
                      {item.key === ExecutionEventType.DATA_SYNC && (
                        <Form.Item name={[field.name, 'dataSyncConfigs']} style={{ marginBottom: 0 }}>
                          <DataSyncConfig preName={name} name={field.name} item={item} />
                        </Form.Item>
                      )}
                    </div>
                  )}
                  <DeleteOutlined
                    className={styles.delete}
                    onClick={() => {
                      remove(field.name);
                    }}
                  />
                </div>
              );
            })}
          </>
        );
      }}
    </Form.List>
  );
};

export default ExecutionEvents;
