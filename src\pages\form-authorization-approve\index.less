.pageBox {
  :global {
    .gwy-ui-layout-page-content {
      padding: 0 !important;
    }
  }
}

.container {
  height: 100%;

  .header {
    height: 40px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #fff;
    padding: 0 20px;

    .submitTime {
      font-weight: 500;
      font-size: 14px;
      color: #86909c;
    }
  }

  .pageContent {
    display: flex;
    height: calc(100% - 40px);
    overflow: hidden;
  }
}

.left {
  height: 100%;
  overflow: hidden;
  width: 220px;
  border-right: 1px solid #c9cdd4;
  padding-top: 12px;

  :global {
    .ant-checkbox-group {
      display: inline-block !important;
      overflow-y: auto;
    }
  }

  .formTotal {
    width: 100%;
    padding: 4px 0 12px 20px;
    font-weight: bold;
    font-size: 16px;
    color: #1d2129;
  }

  .actions {
    box-shadow: 0 0 6px 0 rgba(0, 0, 0, 15%);
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;

    :global {
      .ant-btn {
        border-radius: 2px !important;
      }
    }
  }

  .formItem {
    position: relative;
    width: 100%;
    height: 60px;
    display: flex;
    align-items: center;
    padding: 0 10px;
    border-left: 6px solid transparent;

    &.active {
      border-left-color: #4d7bf6;
      border-radius: 2px;
      background-color: #fff;
    }

    .checkItem {
      display: flex;
      align-items: center;
    }

    .appInfo {
      margin-left: 9px;
      color: #1d2129;
      font-size: 14px;
      font-weight: bold;

      .headerInfo {
        display: flex;
        align-items: center;
        margin-bottom: 6px;
      }

      .createUser {
        color: #4e5969;
        font-size: 12px;
        font-weight: 400;
        max-width: 172px;
      }
    }

    .orgName {
      color: #4e5969;
      font-size: 12px;
      padding-left: 24px;
    }
  }
}

.right {
  position: relative;
  width: calc(100% - 200px);
  height: 100%;
  padding: 20px;
  overflow-y: auto;
}

.permissionConfig {
  .sectionHeader {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    gap: 12px;
  }

  .sectionTitle {
    font-size: 14px;
    font-weight: bold;
    color: #1d2129;
  }

  .sectionContent {
    font-size: 14px;
    color: #1d2129;
    font-weight: 400;
    margin-top: 5px;
  }

  .sectionDesc {
    font-size: 12px;
    color: #4e5969;
  }

  .viewConfigLink {
    font-size: 14px;
    color: #4d7bf6;
    cursor: pointer;
    text-decoration: none;
  }

  .positionTypeSection {
    margin-bottom: 16px;
    border: 1px solid #e5e6eb;
    border-top: none;

    .departmentRow {
      display: flex;
      align-items: center;
      border-top: 1px solid #e5e6eb;
      color: #1d2129;

      :global {
        .ant-checkbox-wrapper {
          font-size: 14px;
          color: #1d2129;
          font-weight: 500;
        }
      }

      .departmentCheckbox {
        padding: 0 20px;
      }

      .positionTypes {
        border-left: 1px solid #e5e6eb;
        padding: 12px 19px;
        display: flex;
        flex-wrap: wrap;
        gap: 12px;
        flex: 1;

        :global {
          .ant-checkbox-wrapper {
            font-size: 12px;
          }
        }
      }
    }
  }

  .functionPermission {
    padding: 16px;
    background: #f7f8fa;
    border-radius: 6px;
    border: 1px solid #e5e6eb;

    .functionTitle {
      font-size: 14px;
      font-weight: 500;
      color: #1d2129;
      margin-right: 12px;
    }

    .functionStatus {
      font-size: 14px;
      color: #4e5969;
    }
  }
}

.departmentCollapse {
  margin-bottom: 24px;

  .userGrid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(85px, 1fr));
    gap: 12px 15px;

    .userItem {
      display: flex;
      flex-direction: column;
      align-items: center;
      position: relative;

      .userAvatar {
        position: relative;
        margin-bottom: 8px;

        .statusTag {
          width: calc(100% - 2px);
          position: absolute;
          bottom: 0;
          right: 1px;
          z-index: 1;
          display: flex;
          justify-content: center;
          align-items: center;
          font-size: 12px;
          color: #fff;
          border-bottom-left-radius: 4px;
          border-bottom-right-radius: 4px;

          &.add {
            background-color: #00b42a;
          }

          &.delete {
            background-color: #f53f3f;
          }
        }
      }

      .removeIcon {
        position: absolute;
        top: -6px;
        right: -6px;
        width: 16px;
        height: 16px;
        cursor: pointer;
      }

      .userInfo {
        text-align: center;
        max-width: 85px;

        .userName {
          font-size: 14px;
          font-weight: 500;
          color: #1d2129;
          margin-bottom: 3px;
        }

        .userPost {
          font-size: 12px;
          color: #86909c;
        }

        .userNote {
          font-size: 11px;
          color: #f53f3f;
          line-height: 1.2;
        }
      }
    }
  }
}

.noData {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #86909c;
  font-size: 14px;
}

.overlayPop {
  :global {
    .ant-popover-inner {
      padding: 0;
    }
  }
}

.popContainer {
  width: 280px;
  height: 160px;
  display: flex;
  flex-direction: column;

  .content {
    padding: 10px 12px;
    flex: 1;
    overflow-y: auto;
  }

  .contentLabel {
    margin-bottom: 4px;

    &::before {
      content: '*';
      color: red;
    }
  }

  .buttons {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    gap: 10px;
    height: 40px;
    padding-right: 6px;
    border-top: 1px solid #e5e6eb;
    flex-shrink: 0;

    :global {
      .ant-btn {
        border-radius: 2px;
        height: 28px;
        width: 72px;
        line-height: 1;
      }
    }
  }

  :global {
    .ant-input {
      background: #f7f8fa;
    }
  }
}
