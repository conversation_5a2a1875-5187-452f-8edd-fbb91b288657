import { IEditorRef } from '@/components/onlyoffice-editor';
import { blankWordUrl } from '@/components/onlyoffice-editor/const';
import { useBridge, useRoute } from '@/hooks';
import { enableFormVersion, getFormVersion, updateFormVersion } from '@/services/app';
import { addSubDataUnitConfig, getSubDataUnitConfigByVersionId } from '@/services/app-sub';
import { getDataUnitAndTag } from '@/services/datasource';
import { deleteFormApply } from '@/services/form-manage';
import { getOrgDataUnits, getOrgDataUnitsTags } from '@/services/org-data-unit';
import { FormVO } from '@/types/app';
import { CalcFieldVO } from '@/types/calc-field';
import { FormCombineTagConfig } from '@/types/combine-tag';
import { DataUnitBaseVO } from '@/types/data-unit';
import { FormField, SourceType } from '@/types/form-field';
import { FormTableConfig } from '@/types/form-table';
import { OrgDataUnitTagListVO } from '@/types/org-data-unit';
import { Post } from '@/types/post';
import { DataUnitTagVO } from '@/types/tag';
import { genOpenMicroApp } from '@/utils';
import { getMetaTagUniqueId } from '@/utils/metadata';
import { Button, ConfigProvider, LayoutPage, message, Modal } from '@gwy/components-web';
import { useTrackedEffect } from 'ahooks';
import classNames from 'classnames';
import { isEmpty, isNil, omit, uniq } from 'lodash-es';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import { refreshAppFormManage } from '../app-form-manage';
import { refreshAppFormPremission } from '../app-form-premission';
import AppHeader from './components/app-header';
import LeftPanel, { LeftPanelTabType } from './components/left-panel';
import RightPanel, { RightPanelTabType } from './components/right-panel';
import { Default_Approve_Buttons, Default_Submit_Buttons } from './components/right-panel/form-design/button-options';
import { PropertyConfigRef } from './components/right-panel/form-design/property-config';
import { validateFormFields } from './components/right-panel/form-design/utils';
import { AppType, FormType, SelectedState, SelectedStateType } from './const';
import { AppDesignerContext } from './context';
import styles from './index.less';

export const openAppDesigner = genOpenMicroApp<IRouteState>('app-designer');

type IRouteState = {
  formVersionId?: number;
  stashFormVersionId?: number;
  post?: Post;
  onOk?: () => void;
};

const AppDesigner = () => {
  const route = useRoute<IRouteState>();
  const bridge = useBridge();

  const {
    formVersionId: defaultFormVersionId = 1416,
    stashFormVersionId,
    onOk,
    post = {
      postId: '1945667382829973507',
      postName: '法定代表人',
      relatePostId: '',
      relatePostName: null,
      relateDepartmentName: null,
      relateOrgName: null,
      relateOrgShortName: null,
      groupId: '',
      userId: '1945666106956251136',
      userName: '猪二',
      portraitUrl: null,
      sex: 'MAN',
      property: 'TOP',
      postType: 'NO',
      personalType: 'NO_PERSON',
      originalPersonalType: 'NO_PERSON',
      userPostRelate: 'NO',
      orgId: '1945667377608065024',
      orgName: '能力有限公司',
      orgShortName: '能力有限',
      departmentId: '1945667382829973504',
      departmentName: '能力有限',
      departmentProperty: 'TOP',
      createTime: null,
      leaveInfo: null,
      key: '1945667382829973507',
      title: '法定代表人',
      value: '1945667382829973507',
      label: '能力有限/法定代表人',
      selectable: true,
      isLeaf: true,
    },
  } = route.state || {};

  // 表单版本id
  const [formVersionId, setFormVersionId] = useState<number>(stashFormVersionId ?? defaultFormVersionId);
  // 岗位列表
  const [postList, setPostList] = useState<Post[]>([]);
  // 表单名称
  const [name, setName] = useState('');
  // 表单类型
  const [formType, setFormType] = useState<FormType>(FormType.Normal);
  // 应用类型
  const [appType, setAppType] = useState<AppType>(AppType.SUBMIT);
  // word 模式
  const [wordReadonly, setWordReadonly] = useState(true);
  // 通知配置
  const [notifyConfig, setNotifyConfig] = useState<any>();
  // 左右面板tab
  const [leftPanelTabType, setLeftPanelTabType] = useState(LeftPanelTabType.baseInfo);
  // 右面板tab
  const [rightPanelTabType, setRightPanelTabType] = useState(RightPanelTabType.FormDesign);

  // 可选的数据单元
  const [availableDataUnits, setAvailableDataUnits] = useState<DataUnitBaseVO[]>([]);

  // 基础分组已配置的数据单元
  const [configuredDataUnits, setConfiguredDataUnits] = useState<DataUnitBaseVO[]>([]);
  const [baseGroupName, setBaseGroupName] = useState('');
  // 基础分组已配置的 FormItem（包含标签、字段）
  const [configuredFields, setConfiguredFields] = useState<FormField[]>([]);
  // 组合标签配置
  const [combineTagConfig, setCombineTagConfig] = useState<FormCombineTagConfig>();
  // 数据表格列表
  const [tableList, setTableList] = useState<FormTableConfig[]>([]);
  // 按钮列表
  const [buttonConfigs, setButtonConfigs] = useState<any[]>([]);
  // 字段列表
  const [calcFields, setCalcFields] = useState<CalcFieldVO[]>([]);

  // 聚合表单---------------start
  // 是否聚合
  const [groupFlag, setGroupFlag] = useState<boolean | undefined>();
  // 聚合-可选的主表单
  const [aggregationMainDataUnits, setAggregationMainDataUnits] = useState<DataUnitBaseVO[]>([]);
  // 聚合-选择的主表单
  const [aggregationMainDataUnitId, setAggregationMainDataUnitId] = useState<number>();
  // 聚合-可选的子表单
  const [aggregationSubDataUnits, setAggregationSubDataUnits] = useState<DataUnitBaseVO[]>([]);
  // 当前选中的子表单
  const [currSubDataUnitId, setCurrSubDataUnitId] = useState<number>();
  // 聚合表单---------------end

  // 基础分组的数据单元Ids
  const baseGroupDataUnitIds = configuredDataUnits.map((item) => item.dataUnitId) || [];
  // 所有数据表格的数据单元Ids
  const tableListDataUnitIds = useMemo(() => {
    return tableList?.reduce((acc, cur) => {
      const ids = cur.dataUnits?.map((d) => d.dataUnitId) || [];
      acc.push(...ids);
      return acc;
    }, [] as number[]);
  }, [tableList]);
  const allConfiguredDataUnitsIds = uniq([...baseGroupDataUnitIds, ...tableListDataUnitIds, aggregationMainDataUnitId].filter(Boolean));
  const allConfiguredDataUnitsIdsStr = allConfiguredDataUnitsIds.join('');
  const [allConfiguredDataUnitsWithTags, setAllConfiguredDataUnitsWithTags] = useState<OrgDataUnitTagListVO[]>([]);
  const [allDataUnitsWithTags, setAllDataUnitsWithTags] = useState<DataUnitBaseVO[]>([]);
  const tagsMap = allConfiguredDataUnitsWithTags?.reduce((acc, cur) => {
    cur.tagList?.forEach((tag) => {
      acc[getMetaTagUniqueId(tag)] = tag;
    });
    return acc;
  }, {} as Record<string, DataUnitTagVO & { $$dataUnit?: OrgDataUnitTagListVO }>);

  // 选中的项
  const [selectedState, setSelectedState] = useState<SelectedState>({ type: SelectedStateType.baseGroup });

  // 编辑器ref
  const editorRef = useRef<IEditorRef>();

  // 属性配置ref（切换时，需要校验必填
  const propertyConfigRef = useRef<PropertyConfigRef>(null);
  const getPropertyConfigRef = useCallback(() => propertyConfigRef.current, []);

  // 应用信息
  const [appInfo, setAppInfo] = useState<FormVO>();

  const [loading, setLoading] = useState(false);

  // 整张表单中表格以及基础分组，组合标签配置，中的字段
  const configuredFormFields = useMemo(() => {
    console.log('configuredFields', configuredFields, combineTagConfig, tableList);
    const fieldHadCfgInFields = configuredFields?.filter((field) => [SourceType.Field].includes(field.sourceType));
    let fieldHadCfgInTable = [];
    tableList?.forEach((table) => {
      if (table?.fields?.length > 0) {
        fieldHadCfgInTable = [...fieldHadCfgInTable, ...(table?.fields?.filter((field) => [SourceType.Field].includes(field.sourceType)) || [])];
      }
    });
    const fieldHadCfgInCombineTag = combineTagConfig?.fields?.map?.((field) => [SourceType.Field].includes(field.sourceType)) || [];
    return [...fieldHadCfgInFields, ...fieldHadCfgInTable, ...fieldHadCfgInCombineTag];
  }, [tableList, combineTagConfig, configuredFields]);

  const getDefaultButtonConfigs = (data) => {
    return !isNil(data.extConfig?.buttonConfigs)
      ? data.extConfig?.buttonConfigs
      : data.appType === AppType.SUBMIT
      ? Default_Submit_Buttons
      : Default_Approve_Buttons;
  };

  const fetchAppInfo = async () => {
    if (!formVersionId) return;
    const data: FormVO = await getFormVersion(formVersionId);
    setAppInfo(data);
    editorRef.current?.setFileUrl({
      fileUrl: data?.wordFileUrl || blankWordUrl,
    });

    setName(data.name);
    // setFormType(data.formType || FormType.Normal);
    setAppType(data.appType);
    setGroupFlag(data.groupFlag ?? false);
    setWordReadonly(data.wordReadonly ?? true);
    setNotifyConfig(data.notifyConfig);

    setConfiguredDataUnits(!isEmpty(data.dataUnits) ? data.dataUnits : [{}]);
    setBaseGroupName(data.extConfig?.baseGroupName);
    setConfiguredFields(data.extConfig?.fields || []);
    setCombineTagConfig(data.extConfig?.combineTagConfig);
    setTableList(data.extConfig?.tableConfigs || []);
    setButtonConfigs(getDefaultButtonConfigs(data));
    setCalcFields(data.fieldVOS || []);

    // 聚合表单
    if (data.groupFlag) {
      const mainDataUnitId = data.dataUnits[0]?.dataUnitId;
      setAggregationMainDataUnitId(mainDataUnitId);
      setCurrSubDataUnitId(mainDataUnitId);
    }
  };

  // 获取组织下的数据单元
  const fetchOrgDataUnits = async () => {
    const data = await getOrgDataUnits({
      orgId: post.orgId,
    });
    setAvailableDataUnits(data);
  };

  const fetchDataUnitAndTag = async () => {
    const data = await getDataUnitAndTag({ postId: post?.postId });
    setAllDataUnitsWithTags(data);
  };

  const init = async () => {
    fetchOrgDataUnits();
    fetchDataUnitAndTag();
    fetchAppInfo();
  };

  useEffect(() => {
    init();
  }, []);

  // 获取已配置的数据单元的标签（包含基础分组和数据表格）
  const fetchAllConfiguredDataUnitsWithTags = async () => {
    const params = {
      orgId: post?.orgId,
      dataUnitIds: allConfiguredDataUnitsIds,
    };
    const dataUnits: OrgDataUnitTagListVO[] = await getOrgDataUnitsTags(params);
    // 数据处理，方便使用
    dataUnits?.forEach((dataUnit, index) => {
      dataUnit.isMainUnite = index === 0;
      dataUnit.tagList?.forEach((tag) => {
        tag.$$dataUnit = omit(dataUnit, 'tagList');
      });
    });
    setAllConfiguredDataUnitsWithTags(dataUnits);
  };

  useEffect(() => {
    if (!allConfiguredDataUnitsIdsStr) {
      setAllConfiguredDataUnitsWithTags([]);
      return;
    }
    fetchAllConfiguredDataUnitsWithTags();
  }, [allConfiguredDataUnitsIdsStr]);

  const close = (isConfirm = false) => {
    if (isConfirm) {
      Modal.confirm({
        title: '确定要关闭吗？',
        content: '如需暂存，请先点击暂存按钮',
        onOk: async () => {
          // 从未主动暂存过，需要删除表单
          if (appInfo.deleteStash) {
            await deleteFormApply(formVersionId, post.postId);
          }
          bridge.close();
        },
      });
    } else {
      bridge.close();
    }
  };

  const getSubmitData = async () => {
    const wordFileUrl = await editorRef.current?.getFileUrl();
    return {
      postId: post.postId,
      formVersionId,
      name,
      appType,
      groupFlag,
      wordFileUrl,
      wordReadonly,
      // storeDataUnitIds,
      dataUnits: configuredDataUnits?.filter((item) => !!item.dataUnitId)?.map((item, index) => ({ ...item, mainDataUnit: index === 0 })),
      notifyConfig: appType === AppType.Approve ? notifyConfig : null,
      extConfig: {
        baseGroupName,
        fields: configuredFields,
        combineTagConfig,
        tableConfigs: tableList,
        buttonConfigs,
      },
      fieldVOS: calcFields,
    };
  };

  // 必填校验
  const validateRequired = () => {
    if (!name) {
      setLeftPanelTabType(LeftPanelTabType.baseInfo);
      return Promise.reject(new Error('表单名称未填写'));
    }

    // 没有数据表格时，基础分组需要校验
    if (isEmpty(tableList)) {
      if (isEmpty(configuredFields) && isEmpty(combineTagConfig?.fields)) {
        return Promise.reject(new Error('请配置基础分组内容'));
      }
    }

    // 数据表格校验
    if (tableList?.some((table) => isEmpty(table.fields))) {
      return Promise.reject(new Error('请配置数据表格内容'));
    }

    // 聚合表单校验
    if (groupFlag) {
      if (!aggregationMainDataUnitId) {
        return Promise.reject(new Error('请选择聚合主表单'));
      }
    }

    return Promise.resolve();
  };

  // 修改
  const handleUpdate = async (isAuto = false) => {
    try {
      await validateRequired();
    } catch (err) {
      message.warning(err.message);
      return Promise.reject();
    }
    await validateFormFields(getPropertyConfigRef());
    if (!isAuto) {
    }

    try {
      setLoading(true);
      let params = await getSubmitData();
      params = {
        ...params,
      };
      const newFormVersionId = await updateFormVersion(formVersionId, params);
      return newFormVersionId;
    } finally {
      setLoading(false);
    }
  };

  // 暂存
  const handleStash = async () => {
    try {
      await handleUpdate();
      await message.success('暂存成功', 1.5);
      onOk?.();
      close();
      refreshAppFormPremission(bridge);
      refreshAppFormManage(bridge);
    } catch (e) {}
  };

  // 发布
  const handleSubmit = async () => {
    try {
      const newFormVersionId = await handleUpdate();
      setLoading(true);
      await enableFormVersion(newFormVersionId, { postId: post?.postId });
      await message.success('发布成功', 1.5);
      onOk?.();
      close();
      refreshAppFormPremission(bridge);
      refreshAppFormManage(bridge);
    } catch (e) {}
    setLoading(false);
  };

  // 聚合表单---------------start

  // 切换聚合开关，需要重置（第一次获取app配置也会设置groupFlag，这时不能重置
  useTrackedEffect(
    (changes, previousDeps, currentDeps) => {
      // console.log('groupFlag===', changes, previousDeps, currentDeps);
      if (changes?.includes(0) && !isNil(previousDeps?.[0])) {
        // console.log('groupFlag===triggerByUser');
        setAggregationMainDataUnitId(undefined);
        setConfiguredDataUnits([{}]);
        setConfiguredFields([]);
        setCombineTagConfig(null);
        setTableList([]);
      }
    },
    [groupFlag],
  );

  // 修改子表单，如果当前当前选中的子表单被删除，需要重新选择

  // 获取子表单配置信息
  const fetchAggregationSubDataUnitConfig = async (dataUnitId) => {
    const data = (await getSubDataUnitConfigByVersionId({ formVersionId, dataUnitId })) || {};
    // 保留筛选条件配置，只改变数据单元
    setConfiguredDataUnits((pre) => {
      const dataUnit = pre[0];
      return [
        {
          ...dataUnit,
          dataUnitId,
        },
      ];
    });
    setBaseGroupName(data.extConfig?.baseGroupName);
    setConfiguredFields(data.extConfig?.fields || []);
    setButtonConfigs(getDefaultButtonConfigs(data));
    editorRef.current?.setFileUrl({ fileUrl: data.wordFileUrl || blankWordUrl });
    setWordReadonly(data.wordReadonly ?? true);
  };

  useEffect(() => {
    if (!currSubDataUnitId) return;
    fetchAggregationSubDataUnitConfig(currSubDataUnitId);
  }, [currSubDataUnitId]);

  // 获取子表单提交参数
  const getAggregationSubSubmitData = async (dataUnitId) => {
    const wordFileUrl = await editorRef.current?.getFileUrl();
    return {
      postId: post.postId,
      formVersionId,
      wordFileUrl,
      wordReadonly,
      dataUnitId,
      extConfig: {
        baseGroupName,
        fields: configuredFields,
        buttonConfigs,
      },
    };
  };

  // 修改子表单
  const handleAggregationSubUpdate = async (dataUnitId) => {
    try {
      await validateRequired();
    } catch (err) {
      message.warning(err.message);
      return Promise.reject();
    }
    await validateFormFields(getPropertyConfigRef());

    try {
      setLoading(true);
      let params = await getAggregationSubSubmitData(dataUnitId);
      params = {
        ...params,
      };
      await addSubDataUnitConfig(params);
    } finally {
      setLoading(false);
    }
  };

  // 获取提交参数
  const getAggregationSubmitData = async () => {
    return {
      postId: post.postId,
      formVersionId,
      name,
      appType,
      groupFlag,
      dataUnits: configuredDataUnits?.map((item, index) => ({
        ...item,
        mainDataUnit: true,
        dataUnitId: aggregationMainDataUnitId,
      })),
      notifyConfig: appType === AppType.Approve ? notifyConfig : null,
      fieldVOS: calcFields,
      extConfig: {},
    };
  };

  // 修改
  const handleAggregationUpdate = async () => {
    try {
      setLoading(true);
      let params = await getAggregationSubmitData();
      params = {
        ...params,
      };
      const newFormVersionId = await updateFormVersion(formVersionId, params);
      return newFormVersionId;
    } finally {
      setLoading(false);
    }
  };

  // 暂存
  const handleAggregationStash = async () => {
    try {
      await handleAggregationSubUpdate(currSubDataUnitId);
      await handleAggregationUpdate();
      await message.success('暂存成功', 1.5);
      onOk?.();
      close();
      refreshAppFormPremission(bridge);
      refreshAppFormManage(bridge);
    } catch (e) {}
  };

  // 发布
  const handleAggregationSubmit = async () => {
    try {
      await handleAggregationSubUpdate(currSubDataUnitId);
      const newFormVersionId = await handleAggregationUpdate();
      setLoading(true);
      await enableFormVersion(newFormVersionId, { postId: post?.postId });
      await message.success('发布成功', 1.5);
      onOk?.();
      close();
      refreshAppFormPremission(bridge);
      refreshAppFormManage(bridge);
    } catch (e) {}
    setLoading(false);
  };
  // 聚合表单---------------end

  const submitDisabled = useMemo(() => {}, []);

  return (
    <DndProvider backend={HTML5Backend}>
      <AppDesignerContext.Provider
        value={{
          setLoading,
          post,
          appInfo,
          formVersionId,
          name,
          setName,
          // formType,
          // setFormType,
          appType,
          setAppType,
          wordReadonly,
          setWordReadonly,
          notifyConfig,
          setNotifyConfig,
          editorRef,
          handleUpdate,
          propertyConfigRef,
          getPropertyConfigRef,
          fetchAllConfiguredDataUnitsWithTags,

          availableDataUnits,
          setAvailableDataUnits,
          configuredDataUnits,
          setConfiguredDataUnits,
          allConfiguredDataUnitsWithTags: allConfiguredDataUnitsWithTags,
          setAllConfiguredDataUnitsWithTags: setAllConfiguredDataUnitsWithTags,
          allDataUnitsWithTags,
          tagsMap,
          baseGroupName,
          setBaseGroupName,
          configuredFields,
          setConfiguredFields,
          combineTagConfig,
          setCombineTagConfig,
          tableList,
          setTableList,
          buttonConfigs,
          setButtonConfigs,
          calcFields,
          setCalcFields,
          configuredFormFields,

          leftPanelTabType,
          setLeftPanelTabType,
          rightPanelTabType,
          setRightPanelTabType,
          selectedState,
          setSelectedState,

          groupFlag,
          setGroupFlag,
          aggregationMainDataUnits,
          setAggregationMainDataUnits,
          aggregationMainDataUnitId,
          setAggregationMainDataUnitId,
          aggregationSubDataUnits,
          setAggregationSubDataUnits,
          currSubDataUnitId,
          setCurrSubDataUnitId,
          handleAggregationSubUpdate,
          handleAggregationUpdate,
          fetchAggregationSubDataUnitConfig,
        }}
      >
        <LayoutPage
          header={
            <AppHeader
              postList={[post]}
              postId={post?.postId}
              onUploadSucceed={(url) => {
                editorRef.current?.setFileUrl({ fileUrl: url });
              }}
              onCancel={() => close(true)}
            />
          }
          contentClassName={styles.layoutContent}
          footerStyle={{
            justifyContent: 'space-between',
            backgroundColor: '#fff',
            borderTop: '1px solid #c9cdd4',
          }}
          footer={
            <>
              <div className={styles.footerTips}>已配置完成应用后，点击【发布】，完成后可在【表单管理】中使用</div>
              <div className={styles.footerBtns}>
                <Button
                  onClick={() => {
                    groupFlag ? handleAggregationStash() : handleStash();
                  }}
                >
                  暂存
                </Button>
                <Button
                  type="primary"
                  onClick={() => {
                    groupFlag ? handleAggregationSubmit() : handleSubmit();
                  }}
                >
                  发布
                </Button>
              </div>
            </>
          }
          loading={loading}
        >
          <div className={styles.content}>
            <div
              className={classNames(styles.left, {
                [styles.wordDesign]: rightPanelTabType === RightPanelTabType.WordDesign,
              })}
            >
              <ConfigProvider
                theme={{
                  token: {
                    fontSize: 12,
                  },
                }}
              >
                <LeftPanel />
              </ConfigProvider>
            </div>
            <div className={styles.right}>
              <RightPanel editorRef={editorRef} />
            </div>
          </div>
        </LayoutPage>
      </AppDesignerContext.Provider>
    </DndProvider>
  );
};

export default AppDesigner;
