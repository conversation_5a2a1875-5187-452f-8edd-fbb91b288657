import { MetaDataType, MetaUnitGeneralType, UnitType } from '@/const/metadata';
import { DataUnitTagVO } from '@/types/tag';

export enum ENTRY_POST_PROPERTY {
  /** 人事岗 */
  PERSONNEL_POST = 5,
  /** 全职岗 */
  FULLTIME_POST = 6,
  /** 副职岗 */
  DEPUTY_POST = 7,
}

export const ENTRY_POST_PROPERTY_LABEL = {
  [ENTRY_POST_PROPERTY.PERSONNEL_POST]: '人事岗',
  [ENTRY_POST_PROPERTY.FULLTIME_POST]: '全职岗',
  [ENTRY_POST_PROPERTY.DEPUTY_POST]: '副职岗',
};

export enum ExecutionEventType {
  POST_ENTRY = 'POST_ENTRY', // 入职
  POST_LEVEL = 'POST_LEVEL', // 离职
  POST_INVITE = 'POST_INVITE', // 入职邀请
  DATA_SYNC = 'DATA_SYNC', // 数据同步
}

export enum PostEntryType {
  SELECT_EXIST = 'SELECT_EXIST', // 选择已有的岗位
  AUTO_CREATE = 'AUTO_CREATE', // 自动生成岗位
}

export interface EventParamsConfig {
  label?: string;
  typeMath?: (tag?: DataUnitTagVO) => boolean;
  fieldName?: string;
  tips?: string;
}

/** 是否是特定的对象 */
export const isSpecificObject = (tag: DataUnitTagVO, generalType: MetaUnitGeneralType) => {
  return tag?.tagMetaDataConfig?.type === MetaDataType.Object && tag?.tagMetaDataConfig?.metaDataObjectDTO?.generalType === generalType;
};

const toUser = {
  label: '用户',
  fieldName: 'toUser',
  typeMath: (tag: DataUnitTagVO) => {
    return isSpecificObject(tag, MetaUnitGeneralType.User);
  },
  tips: '仅能选择使用对象（用户）标签',
};

const toPost = {
  label: '岗位',
  fieldName: 'toPost',
  typeMath: (tag: DataUnitTagVO) => {
    return isSpecificObject(tag, MetaUnitGeneralType.Post);
  },
  tips: '仅能选择使用对象（岗位）标签',
};

const postEntryList = [
  toUser,
  toPost,
  {
    label: '用工类型',
    fieldName: 'toWorkType',
    typeMath: (tag: DataUnitTagVO) => {
      return tag?.tagMetaDataConfig?.type === MetaDataType.Text;
    },
    tips: '仅能选择文本/选项标签',
  },
  {
    label: '合同类别',
    fieldName: 'toContractType',
    typeMath: (tag: DataUnitTagVO) => {
      return tag?.tagMetaDataConfig?.type === MetaDataType.Text && tag?.tagMetaDataConfig?.metaDataTextDTO?.inputType === 'ENUM';
    },
    tips: '仅能选择文本/选项标签',
  },
  {
    label: '入职时间',
    fieldName: 'toEntryTime',
    typeMath: (tag: DataUnitTagVO) => {
      return tag?.tagMetaDataConfig?.type === MetaDataType.DateTime;
    },
    tips: '仅能选择时间标签',
  },
];

export const EventParamsMap: Record<string, EventParamsConfig[]> = {
  [ExecutionEventType.POST_ENTRY]: postEntryList,
  [ExecutionEventType.POST_INVITE]: [
    ...postEntryList,
    {
      label: '基本工资',
      fieldName: 'toSalary',
      typeMath: (tag: DataUnitTagVO) => {
        return (
          tag?.tagMetaDataConfig?.type === MetaDataType.Number ||
          (tag?.tagMetaDataConfig?.type === MetaDataType.Unit && tag?.tagMetaDataConfig?.metaDataUnitDTO?.type === UnitType.amount)
        );
      },
      tips: '仅能选择数字、单位（限定为“金额”类）标签',
    },
    {
      label: '合同期限',
      fieldName: 'toContractPeriod',
      typeMath: (tag: DataUnitTagVO) => {
        return (
          tag?.tagMetaDataConfig?.type === MetaDataType.DateTime ||
          tag?.tagMetaDataConfig?.type === MetaDataType.Text ||
          tag?.tagMetaDataConfig?.type === MetaDataType.Number
        );
      },
      tips: '仅能选择使用文本、数字、时间标签',
    },
    {
      label: '试用期',
      fieldName: 'toTrialPeriod',
      typeMath: (tag: DataUnitTagVO) => {
        return (
          tag?.tagMetaDataConfig?.type === MetaDataType.DateTime ||
          tag?.tagMetaDataConfig?.type === MetaDataType.Text ||
          tag?.tagMetaDataConfig?.type === MetaDataType.Number
        );
      },
      tips: '仅能选择使用文本、数字、时间标签',
    },
  ],
  [ExecutionEventType.POST_LEVEL]: [
    toUser,
    toPost,
    {
      label: '离岗时间',
      fieldName: 'toLevelTime',
      typeMath: (tag: DataUnitTagVO) => {
        return tag?.tagMetaDataConfig?.type === MetaDataType.DateTime;
      },
      tips: '仅能选择时间标签',
    },
  ],
};

/** 入职互斥事件 */
export const entryExclusionEvents = [ExecutionEventType.POST_ENTRY, ExecutionEventType.POST_INVITE, ExecutionEventType.POST_LEVEL];
