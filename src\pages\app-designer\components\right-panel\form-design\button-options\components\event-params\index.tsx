import { MetaDataType } from '@/const/metadata';
import { AppDesignerContext } from '@/pages/app-designer/context';
import { OrgDataUnitTagListVO } from '@/types/org-data-unit';
import { EditOutlined, EyeOutlined, InfoCircleOutlined } from '@ant-design/icons';
import { Button, Checkbox, Form, Modal, Radio, Tooltip } from '@gwy/components-web';
import { isEmpty } from 'lodash-es';
import { useContext, useEffect, useMemo, useState } from 'react';
import { EventParamsConfig, EventParamsMap, ExecutionEventType, PostEntryType } from '../../constants';
import TagTreeSelect, { getTagValue } from '../tag-tree-select';
import styles from './index.less';

interface IProps {
  value?: any;
  onChange?: (value: any) => void;
  item?: { key?: ExecutionEventType; label?: string; disabled?: boolean };
  buttonIndex?: number;
  eventIndex?: number;
}

const EventParams = ({ value, onChange, item, buttonIndex, eventIndex }: IProps) => {
  const { allConfiguredDataUnitsWithTags } = useContext(AppDesignerContext);

  const [form] = Form.useForm();

  const [open, setOpen] = useState(false);
  const postEntryTypeList = Form.useWatch(['postEntryTypeList'], form);

  const [invitePreviewModal, setInvitePreviewModal] = useState({
    visible: false,
    entryInviteTemplateUrl: '',
  });

  const prePath = useMemo(() => {
    return ['buttonList', buttonIndex, 'events', eventIndex];
  }, [buttonIndex, eventIndex]);

  useEffect(() => {
    if (!open) return;
    form.setFieldsValue(value);
  }, [open]);

  const handleOk = async () => {
    try {
      const values = await form.validateFields();
      onChange(values);
      setOpen(false);
    } catch (e) {}
  };

  const handelCancel = () => {
    setOpen(false);
  };

  const getTagsOptions = (params: EventParamsConfig) => {
    const getChildren = (dataUnit: OrgDataUnitTagListVO) => {
      return dataUnit.tagList?.filter(params.typeMath).map((tag) => ({
        title: tag.name,
        value: getTagValue(tag),
      }));
    };
    const options = allConfiguredDataUnitsWithTags
      ?.filter((dataUnit) => getChildren(dataUnit)?.length > 0)
      .map((dataUnit) => {
        return {
          title: dataUnit.name,
          value: dataUnit.dataUnitId,
          disabled: true,
          children: getChildren(dataUnit),
        };
      });
    return options;
  };

  const renderEventParams = () => {
    return (
      <Form form={form} labelAlign="left">
        <Form.Item
          label="入职类型"
          name="postEntryTypeList"
          initialValue={[]}
          rules={[
            {
              required: true,
              message: '请选择',
              validator: async (rule, value) => {
                if ([ExecutionEventType.POST_ENTRY, ExecutionEventType.POST_INVITE].includes(item?.key) && isEmpty(value)) {
                  throw new Error();
                }
                return Promise.resolve();
              },
            },
          ]}
          hidden={![ExecutionEventType.POST_ENTRY, ExecutionEventType.POST_INVITE].includes(item?.key)}
        >
          <Checkbox.Group
            onChange={(value) => {
              form.resetFields();
              form.setFieldsValue({ postEntryTypeList: value });
            }}
          >
            <Checkbox value={PostEntryType.SELECT_EXIST}>选择已有岗位</Checkbox>
            <Checkbox value={PostEntryType.AUTO_CREATE}>
              自动生成岗位{' '}
              <Tooltip title={`若选中该选项，则无法批量入职，参数中标签无法选择到数据表格中的标签`}>
                <InfoCircleOutlined style={{ color: '#4E5969' }} />
              </Tooltip>
            </Checkbox>
          </Checkbox.Group>
        </Form.Item>

        <div style={{ fontWeight: 'bold', marginBottom: 8 }}>{item?.key === ExecutionEventType.POST_LEVEL ? '离职信息' : `入职信息`}</div>
        <div className={styles.listWrapper}>
          {(EventParamsMap[item?.key] || []).map((param, index) => {
            const hidePost =
              [ExecutionEventType.POST_ENTRY, ExecutionEventType.POST_INVITE].includes(item?.key) &&
              !postEntryTypeList?.includes(PostEntryType.SELECT_EXIST) &&
              param.fieldName === 'toPost';
            return (
              <div key={index} className={styles.eventParamItem}>
                <Form.Item
                  label="标签"
                  name={param.fieldName}
                  rules={[{ required: !hidePost, message: '请选择标签' }]}
                  hidden={hidePost}
                  style={{ marginBottom: index === (EventParamsMap[item?.key] || []).length - 1 ? 0 : 16 }}
                >
                  <TagTreeSelect style={{ width: 200 }} treeData={getTagsOptions(param)} />
                </Form.Item>
                {!hidePost && (
                  <Form.Item>
                    <div className={styles.itemRight}>
                      <span className={styles.midLabel}>传参至</span>
                      <span className={styles.rightLabel}>
                        {param.label}{' '}
                        <Tooltip title={param.tips}>
                          <InfoCircleOutlined style={{ color: '#4E5969' }} />
                        </Tooltip>
                      </span>
                    </div>
                  </Form.Item>
                )}
              </div>
            );
          })}
        </div>

        {item?.key === ExecutionEventType.POST_INVITE && (
          <>
            <div className={styles.selectWrapper}>
              <Form.Item label="邀请函" style={{ flexGrow: 1 }} name="entryInviteType" initialValue={0}>
                <Radio.Group>
                  <Radio value={0}>系统默认</Radio>
                  <Radio value={1} disabled>
                    自定义
                  </Radio>
                </Radio.Group>
              </Form.Item>
              <Button
                type="link"
                icon={<EyeOutlined />}
                style={{ padding: 0 }}
                onClick={() => {
                  setInvitePreviewModal((pre) => {
                    return {
                      ...pre,
                      visible: true,
                    };
                  });
                }}
              >
                预览
              </Button>
            </div>
            <div className={styles.eventParamItem}>
              <Form.Item label="邀请函附建" style={{ flexGrow: 1, marginBottom: 0 }} name="toAttachment">
                <TagTreeSelect treeData={getTagsOptions({ typeMath: (tag) => tag?.tagMetaDataConfig?.type === MetaDataType.File })} />
              </Form.Item>
              <Form.Item>
                <div className={styles.itemRight}>
                  <span className={styles.midLabel}>传参至</span>
                  <span className={styles.rightLabel}>邀请函</span>
                </div>
              </Form.Item>
            </div>
          </>
        )}
      </Form>
    );
  };

  const renderModalContent = () => {
    return renderEventParams();
  };

  return (
    <>
      <Button block icon={<EditOutlined />} onClick={() => setOpen(true)}>
        {item?.label}
      </Button>
      <Modal destroyOnHidden title={item?.label} open={open} onOk={handleOk} onCancel={handelCancel} width={500}>
        <div className={styles.content}>{renderModalContent()}</div>
      </Modal>

      <Modal
        size="large"
        title="预览邀请函"
        open={invitePreviewModal.visible}
        onCancel={() =>
          setInvitePreviewModal((pre) => {
            return {
              ...pre,
              visible: false,
            };
          })
        }
        centered
        footer={false}
      >
        <img
          width={'100%'}
          className={styles.previewImg}
          src="https://doc.gongwuyun.com/ngwy/static/static-images/gwyweb/images/invite_post_preview.png"
        />
      </Modal>
    </>
  );
};

export default EventParams;
