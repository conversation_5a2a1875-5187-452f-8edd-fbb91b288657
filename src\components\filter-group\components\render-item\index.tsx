import { DATA_TYPE, DYNAMIC_SCOPE, FILTER_VALUE_TYPE, NEW_RELATION_TYPE } from '@/components/filter-group/filter-const';
import { UseIn } from '@/components/meta-dynamic-item/const';
import ObjectItem from '@/components/meta-dynamic-item/object/object-item';
import TextMeta from '@/components/meta-dynamic-item/text';
import UnitMeta from '@/components/meta-dynamic-item/unit';
import { MetaDataType, MetaUnitGeneralType } from '@/const/metadata';
import { FieldType } from '@/types/calc-field';
import { Form, FormInstance, Input, InputNumber, Select, TreeSelect } from '@gwy/components-web';
import { cloneDeep } from 'lodash-es';
import { useMemo } from 'react';
import { UseInStageEnum } from '../..';
import { getFilterValueOptions, getMetaDataObjectConfigList } from '../../utils';
import NumberRange from '../number-filter';
import CustomDateRangePicker, { CustomDatePicker } from '../time-filter';
// import ObjectMeta from ''

interface Iprops {
  form: FormInstance;
  prefix: any[];
  currentTag: any;
  parenntPrefix: any[];
  tags: any;
  relation?: any;
  disabled?: boolean;
  canRelatedTags?: any[];
  calcFields?: any[];
  useInStage?: UseInStageEnum;
}

/**
 * 根据改类型下的运算 没有value
 */
const PICK_EMPTY_VALUE = [
  `${DATA_TYPE.TEXT.val}-${NEW_RELATION_TYPE.EMPTY.val}`,
  `${DATA_TYPE.TEXT.val}-${NEW_RELATION_TYPE.NOT_EMPTY.val}`,
  // `${DATA_TYPE.TEXT.val}-${NEW_RELATION_TYPE.POST_UNDO.val}`,
  // `${DATA_TYPE.TEXT.val}-${NEW_RELATION_TYPE.POST_DONE.val}`,
  // `${DATA_TYPE.TEXT.val}-${NEW_RELATION_TYPE.POST_PART_DONE.val}`,
  // `${DATA_TYPE.TEXT.val}-${NEW_RELATION_TYPE.PART_EMPTY.val}`,
  `${DATA_TYPE.NUMBER.val}-${NEW_RELATION_TYPE.EMPTY.val}`,
  `${DATA_TYPE.NUMBER.val}-${NEW_RELATION_TYPE.NOT_EMPTY.val}`,
  `${DATA_TYPE.NUMBER.val}-${NEW_RELATION_TYPE.MAX.val}`,
  `${DATA_TYPE.NUMBER.val}-${NEW_RELATION_TYPE.MIN.val}`,
  // `${DATA_TYPE.NUMBER.val}-${NEW_RELATION_TYPE.POST_UNDO.val}`,
  // `${DATA_TYPE.NUMBER.val}-${NEW_RELATION_TYPE.POST_DONE.val}`,
  // `${DATA_TYPE.NUMBER.val}-${NEW_RELATION_TYPE.POST_PART_DONE.val}`,
  // `${DATA_TYPE.NUMBER.val}-${NEW_RELATION_TYPE.PART_EMPTY.val}`,
  `${DATA_TYPE.UNIT.val}-${NEW_RELATION_TYPE.EMPTY.val}`,
  `${DATA_TYPE.UNIT.val}-${NEW_RELATION_TYPE.NOT_EMPTY.val}`,
  `${DATA_TYPE.UNIT.val}-${NEW_RELATION_TYPE.NEWEST_DATE.val}`,
  // `${DATA_TYPE.UNIT.val}-${NEW_RELATION_TYPE.POST_UNDO.val}`,
  // `${DATA_TYPE.UNIT.val}-${NEW_RELATION_TYPE.POST_DONE.val}`,
  // `${DATA_TYPE.UNIT.val}-${NEW_RELATION_TYPE.POST_PART_DONE.val}`,
  // `${DATA_TYPE.UNIT.val}-${NEW_RELATION_TYPE.PART_EMPTY.val}`,
];
const RenderDynamicItem = (props: Iprops) => {
  const { form, prefix, currentTag, parenntPrefix, canRelatedTags, relation, disabled, tags, calcFields, useInStage } = props;
  const valueType = Form.useWatch([...parenntPrefix, 'valueType'], form);

  // 根据belongUnitId将数组其分组并转化为树形结构
  const getTagOptions = (tagList) => {
    const groupObj = tagList.reduce((acc, cur) => {
      const { belongDataUnitId, dataUnitId } = cur;
      const groupKey = belongDataUnitId ? belongDataUnitId : dataUnitId;
      if (!acc[groupKey]) {
        acc[groupKey] = [];
      }
      acc[groupKey].push(cur);
      return acc;
    }, {});
    return Object.keys(groupObj).map((key) => {
      return {
        label: groupObj[key]?.[0]?.belongDataUnitName || groupObj[key]?.[0]?.dataUnitName,
        value: groupObj[key]?.[0]?.groupKey,
        disabled: true,
        children: groupObj[key],
      };
    });
  };
  const groupTagsByBelongUnitIds = useMemo(() => {
    return getTagOptions(canRelatedTags);
  }, [canRelatedTags]);

  const getDetailType = (tag) => {
    const { type } = tag?.tagMetaDataConfig || {};
    if ([DATA_TYPE.NUMBER.val, DATA_TYPE.DATETIME.val, DATA_TYPE.CODE.val, DATA_TYPE.UNIT.val, DATA_TYPE.PICTURE.val].includes(type)) {
      return type;
    } else if (type === DATA_TYPE.TEXT.val) {
      const { metaDataTextDTO } = tag?.tagMetaDataConfig || {};
      return metaDataTextDTO?.inputType === 'MANUAL' ? DATA_TYPE.TEXT.val : DATA_TYPE.TEXTENUM.val;
    } else if (type === DATA_TYPE.OBJECT.val) {
      const { metaDataObjectDTO } = tag?.tagMetaDataConfig || {};
      const { generalType } = metaDataObjectDTO || {};
      let tagType = DATA_TYPE.OBJECT.val;
      switch (generalType) {
        case MetaUnitGeneralType.Company:
          tagType = DATA_TYPE.ORG.val;
          break;
        case MetaUnitGeneralType.Department:
          tagType = DATA_TYPE.DEPT.val;
          break;
        case MetaUnitGeneralType.Post:
          tagType = DATA_TYPE.POST.val;
          break;
        case MetaUnitGeneralType.User:
          tagType = DATA_TYPE.USER.val;
          break;
        default:
          tagType = DATA_TYPE.OBJECT.val;
      }
      return tagType;
    } else {
      return type;
    }
  };
  const customtagCheck = (type, tag, targetTag) => {
    switch (type) {
      case MetaDataType.Unit: {
        const { type: checkTagType } = tag.tagMetaDataConfig?.metaDataUnitDTO || {};
        const { type: TargetTagType } = targetTag.tagMetaDataConfig?.metaDataUnitDTO || {};
        return checkTagType === TargetTagType;
      }
      default: {
        return true;
      }
    }
    // return false;
  };

  const checkTagTypeEqual = (tag, targetTag) => {
    const curTagType = getDetailType(tag);
    const targetType = getDetailType(targetTag);
    const isEqualType = curTagType === targetType;
    let customCheck = true;
    if (curTagType === targetType && targetType === MetaDataType.Unit) {
      customCheck = customtagCheck(targetType, tag, targetTag);
    }
    return isEqualType && customCheck;
  };
  const getTreeTagOps = (groupTagAndFields, targetTag) => {
    // const tagAndFieldsTreeData
    // const unitHasTags = getCanuseRelateTags()
    // console.log(groupTagAndFields, 'groupTagAndFields------------')
    const { type, tagId } = targetTag || {};
    const resluts = cloneDeep(groupTagAndFields || [])?.filter((unit) => {
      if (unit?.title === '字段') {
        return true;
      } else {
        let flag = false;
        // console.log(unit, 'children')
        unit.children = unit.children?.filter((tag) => checkTagTypeEqual(tag, targetTag) && tagId !== tag.tagId);
        // console.log(unit.children, 'children')
        if (unit?.children?.length > 0) {
          flag = true;
        }
        return flag;
      }
    });
    // console.log(resluts, 'results----------')
    return resluts;
  };

  const getTreeOptions = (data: any) => {
    let treeOptions = [];
    treeOptions = data?.map((ops: any) => {
      return {
        title: ops.item,
        value: ops.id,
        label: ops.item,
        children: ops.subEnumList && getTreeOptions(ops.subEnumList),
      };
    });
    return treeOptions;
  };

  const getFieldControls = (type) => {
    switch (type) {
      case DATA_TYPE.TEXT.val: {
        return <Input placeholder={'请输入'} style={{ width: '100%' }} disabled={disabled} />;
      }
      case DATA_TYPE.NUMBER.val: {
        if ([NEW_RELATION_TYPE.BETWEEN.val, NEW_RELATION_TYPE.NOT_BETWEEN.val].includes(relation)) {
          return <NumberRange disabled={disabled} />;
        }
        return <InputNumber placeholder="请输入" style={{ width: '100%' }} disabled={disabled} />;
      }
      case DATA_TYPE.DATETIME.val: {
        if ([NEW_RELATION_TYPE.BETWEEN.val].includes(relation)) {
          return <CustomDateRangePicker style={{ width: '100%' }} disabled={disabled} />;
        }
        return (
          <CustomDatePicker
            showTime={{
              hideDisabledOptions: true,
            }}
            style={{ width: '100%' }}
            disabled={disabled}
          />
        );
      }
      case DATA_TYPE.CODE.val: {
        return <Input placeholder="请输入" disabled={disabled} />;
      }
      case DATA_TYPE.DEPT.val:
      case DATA_TYPE.USER.val:
      case DATA_TYPE.ORG.val:
      case DATA_TYPE.OBJECT.val: {
        return <ObjectItem useIn={UseIn.Filter} tag={currentTag} disabled={disabled} />;
      }
      case DATA_TYPE.TEXTENUM.val: {
        const { metaDataTextDTO } = currentTag?.tagMetaDataConfig || {};
        const { enumList } = metaDataTextDTO || {};
        return (
          <TextMeta tag={currentTag} disabled={disabled} />
          // <TreeSelect
          //   multiple
          //   placeholder={'请选择'}
          //   allowClear
          //   treeDefaultExpandAll
          //   disabled={disabled}
          //   treeData={getTreeOptions(enumList)}
          //   dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
          // />
        );
      }
      case DATA_TYPE.UNIT.val: {
        return <UnitMeta tag={currentTag} styles={{ display: 'flex' }} disabled={disabled} />;
      }
      default: {
        return null;
      }
    }
  };

  const renderItem = useMemo(() => {
    // const { type } = currentTag?.tagMetaDataConfig || {};
    // console.log(type, currentTag, 'dataType-----');
    const type = getDetailType(currentTag);
    console.log(type, 'type----->');
    if (!PICK_EMPTY_VALUE.includes(`${type}-${relation}`)) {
      if (valueType === FILTER_VALUE_TYPE.VARIABLE) {
        const treeTagOps = getTreeTagOps(cloneDeep(groupTagsByBelongUnitIds), currentTag);
        console.log(treeTagOps, 'treeTagOps----->');
        return (
          <>
            <Form.Item name={[...prefix, 'refTagId']} style={{ marginBottom: '0px' }}>
              <TreeSelect
                placeholder="请选择标签"
                allowClear
                disabled={disabled}
                treeData={treeTagOps}
                treeDefaultExpandAll
                styles={{
                  popup: { root: { maxHeight: 400, overflow: 'auto' } },
                }}
                virtual={false}
                onChange={(value) => {
                  const tag = canRelatedTags.find((tag) => tag.tagId === value);
                  // 需要处理标签，将当前所属的数据单元添加
                  form.setFieldValue([...parenntPrefix, 'refDataUnitId'], tag?.belongDataUnitId);
                  form.setFieldValue([...parenntPrefix, 'refCode'], tag?.code);
                }}
              />
            </Form.Item>
            <Form.Item name={[...prefix, 'refDataUnitId']} hidden />
            <Form.Item name={[...prefix, 'refCode']} hidden />
          </>
        );
      } else if ([FILTER_VALUE_TYPE.CONST].includes(valueType)) {
        if (relation === NEW_RELATION_TYPE.DYNAMIC_RANGE.val) {
          let options = Object.values(DYNAMIC_SCOPE).map((n) => ({ value: n.value, label: n.text, dependencies: n.dependencies }));
          const configList = getMetaDataObjectConfigList(currentTag);

          if (type === DATA_TYPE.DATETIME.val) {
            options = options.filter((n) => n.dependencies.includes('DATETIME'));
            return (
              <div style={{ display: 'flex' }}>
                <Form.Item
                  style={{ flex: '1', marginRight: '8px', marginBottom: 0 }}
                  rules={[{ required: true, message: '请选择条件' }]}
                  name={[...prefix, 'dateDynamicScope']}
                >
                  <Select options={options} placeholder="请选择范围" style={{ width: '100%' }} disabled={disabled} />
                </Form.Item>
              </div>
            );
          } else if (
            [DATA_TYPE.OBJECT.val, DATA_TYPE.ORG.val, DATA_TYPE.DEPT.val, DATA_TYPE.POST.val, DATA_TYPE.USER.val].includes(type) &&
            configList?.length > 0
          ) {
            options = options.filter((n) => configList.includes(n.value));
            return (
              <div style={{ display: 'flex' }}>
                <Form.Item
                  style={{ flex: '1', marginRight: '8px', marginBottom: 0 }}
                  rules={[{ required: true, message: '请选择条件' }]}
                  name={[...prefix, 'objectDynamicScope']}
                >
                  <Select options={options} placeholder="请选择范围" style={{ width: '100%' }} disabled={disabled} />
                </Form.Item>
              </div>
            );
          }
          return (
            <div style={{ display: 'flex' }}>
              <Form.Item
                style={{ flex: '1', marginRight: '8px', marginBottom: 0 }}
                rules={[{ required: true, message: '请选择条件' }]}
                name={[...prefix, 'objectDynamicScope']}
              >
                <Select options={options} placeholder="请选择范围" style={{ width: '100%' }} disabled={disabled} />
              </Form.Item>
            </div>
          );
        } else {
          return (
            <Form.Item name={[...prefix, 'value']} style={{ marginBottom: '0px' }}>
              {getFieldControls(type)}
            </Form.Item>
          );
        }
      } else if (valueType === FILTER_VALUE_TYPE.VARIABLE_DYNAMIC) {
        return null;
      } else if (valueType === FILTER_VALUE_TYPE.VARIABLE_FIELD) {
        return (
          <Form.Item name={[...prefix, 'refFieldId']}>
            <Select
              placeholder={'请选择'}
              disabled={disabled}
              options={calcFields
                ?.filter((filed) => filed.fieldType === FieldType.IndependentField)
                ?.map((item) => ({ label: item.fieldName, value: item.fieldId }))}
            />
          </Form.Item>
        );
      } else {
        return (
          <>
            <Select placeholder={'请选择'} disabled={disabled} options={[]}></Select>
          </>
        );
      }
    } else {
      return <span>全部数据</span>;
    }
  }, [currentTag, valueType, relation]);
  const getFilterValueOps = () => {
    let ops = getFilterValueOptions(currentTag, relation);
    if ([UseInStageEnum.DataSync, UseInStageEnum.DynamicParams].includes(useInStage)) {
      ops = ops?.filter((op) => op.value !== FILTER_VALUE_TYPE.VARIABLE_DYNAMIC);
    }
    return ops;
  };

  return [NEW_RELATION_TYPE.EMPTY.val, NEW_RELATION_TYPE.NOT_EMPTY.val, NEW_RELATION_TYPE.NEWEST_DATE.val].includes(relation) ? null : (
    <>
      <Form.Item name={[...prefix, 'valueType']} style={{ width: '100px' }} rules={[{ required: true, message: '请选择' }]}>
        <Select
          placeholder="请选择"
          disabled={disabled}
          options={getFilterValueOps()}
          onChange={() => {
            form.setFieldValue([...parenntPrefix, 'value'], undefined);
          }}
        />
      </Form.Item>
      <Form.Item shouldUpdate style={{ flex: 1 }}>
        {renderItem}
      </Form.Item>
    </>
  );
};

export default RenderDynamicItem;
