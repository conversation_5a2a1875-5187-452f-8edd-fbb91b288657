import IconFormExcute from '@/assets/app-form-manage/icon-form-execution.png';
import EditorPreview from '@/components/editor-preview';
import AddressMeta from '@/components/meta-dynamic-item/address';
import CodeMeta from '@/components/meta-dynamic-item/code';
import FileMeta from '@/components/meta-dynamic-item/file';
import NumberMeta from '@/components/meta-dynamic-item/number';
import ObjectMeta from '@/components/meta-dynamic-item/object';
import TextMeta from '@/components/meta-dynamic-item/text';
import TimeMeta from '@/components/meta-dynamic-item/time';
import UnitMeta from '@/components/meta-dynamic-item/unit';
import { MetaDataType } from '@/const/metadata';
import { useBridge, useRoute } from '@/hooks';
import { appAPI, orgDataUnitAPI, systemDatametaAPI } from '@/services';
import { FormVO } from '@/types/app';
import { SourceType } from '@/types/form-field';
import { DataUnitTagVO } from '@/types/tag';
import { getMetaTagUniqueId } from '@/utils/metadata';
import { DoubleRightOutlined, InfoCircleOutlined, SearchOutlined } from '@ant-design/icons';
import { But<PERSON>, Col, Form, Input, LayoutPage, message, Modal, Row, TextEllipsisTooltip, Tooltip } from '@gwy/components-web';
import classNames from 'classnames';
import dayjs from 'dayjs';
import { isEmpty, uniq } from 'lodash-es';
import { memo, useEffect, useMemo, useState } from 'react';
import styles from './index.less';
import TableAndCombine from './table-combine';

export const formatDateTime = (dateTimeStr) => {
  if (!dateTimeStr) return '';

  const dateTime = dayjs(dateTimeStr);
  let formatStr = 'YYYY/MM/DD HH:mm';

  // 当年时间，不展示年份
  if (dateTime.year() === dayjs().year()) {
    formatStr = 'MM/DD HH:mm';
  }

  return dateTime.format(formatStr);
};

const SystemDataMetaApprove = memo(() => {
  const [searchValue, setSearchValue] = useState('');
  const bridge = useBridge();
  const route = useRoute();
  const { bizParam, receivePostId, refreshWaitingList } = (route as any).state || {};

  const [formLists, setFormLists] = useState([]);
  const [formDetail, setFormDetail] = useState(null);
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();
  // 动态表格信息
  const [tableList, setTbaleList] = useState([]);
  const [previewModal, setPreviewModal] = useState<{
    open?: boolean;
    url?: string;
  }>({
    open: false,
    url: '',
  });

  const findFormList = async (approveIds) => {
    setLoading(true);
    try {
      const data = await systemDatametaAPI.getOrgDataUnitStashList({
        formDataDraftIds: approveIds,
      });
      const formDataDraftId = data[0]?.formDataDraftId;
      if (formDataDraftId) {
        getFormDetail(formDataDraftId);
        setFormDetail(data[0]);
      } else {
        setLoading(false);
      }
      setFormLists(data);
    } catch (e) {
      setLoading(false);
    }
  };
  const getFormDetail = async (formDataDraftId) => {
    setLoading(true);
    try {
      const data = await systemDatametaAPI.getOrgDataUnitApproveData(formDataDraftId);
      setFormDetail((prev) => ({ ...prev, ...data }));
    } catch (e) {}
    setLoading(false);
  };

  useEffect(() => {
    bizParam.approveIds.length > 0 && findFormList(bizParam.approveIds);
  }, [bizParam]);

  const [formVersion, setFormVersion] = useState<FormVO>({});
  const cFields = useMemo(() => (formVersion?.extConfig?.fields || []).map((item) => ({ ...item, readonly: true })), [formVersion]);
  const [cTagsMap, setCTagsMap] = useState<Record<number, DataUnitTagVO>>({});
  const fetchTags = async (dataUnitIds) => {
    const dataUnits = await orgDataUnitAPI.getOrgDataUnitsTags({ orgId: formDetail?.creatorPostId, dataUnitIds });
    setCTagsMap(
      dataUnits?.reduce((acc, cur) => {
        cur.tagList?.forEach((tag) => {
          acc[getMetaTagUniqueId(tag)] = tag;
        });
        return acc;
      }, {} as Record<number, DataUnitTagVO>),
    );
  };

  const fetchFormVersion = async () => {
    const formVersion = await appAPI.getFormVersion(formDetail?.formVersionId);
    setFormVersion(formVersion);
    const commonGroupIds = formVersion.dataUnits.map((item) => item.dataUnitId) || [];
    const combineTagIds = formVersion?.extConfig?.combineTagConfig?.fields?.map((field) => field?.dataUnitId) || [];
    const tableUnitIds =
      (formVersion?.extConfig?.tableConfigs || [])?.reduce((acc, cur) => {
        const ids = cur.dataUnits?.map((d) => d.dataUnitId) || [];
        acc.push(...ids);
        return acc;
      }, [] as number[]) || [];
    const allDataUnitIds = uniq([...commonGroupIds, ...combineTagIds, ...tableUnitIds]);
    fetchTags(allDataUnitIds);
    setTbaleList(formVersion?.extConfig?.tableConfigs || []);
    // fetchTags(formVersion.dataUnits.map((item) => item.dataUnitId));
  };

  useEffect(() => {
    if (!formDetail?.formVersionId) return;
    form.resetFields();
    fetchFormVersion();
  }, [formDetail?.formVersionId]);

  const geneModal = (title, func) => {
    return Modal.confirm({
      centered: true,
      title: title,
      onOk: () => func(),
    });
  };
  const handleAgree = async () => {
    const res = await systemDatametaAPI.submitDataAgree(formDetail.formDataDraftId, {
      postId: receivePostId,
    });
    message.success('操作成功');
    if (formLists.length > 1) {
      findFormList(bizParam.approveIds?.filter((id) => id !== formDetail.formDataDraftId));
    } else {
      refreshWaitingList();
      bridge.close();
    }
  };

  const handleStatus = async (staus) => {
    try {
      const { message } = await form.validateFields();
      const api = staus === 'reject' ? 'submitDataReject' : 'submitDataBack';
      const tittle = staus === 'reject' ? '拒绝' : '退回';
      const excuteFn = async () => {
        setLoading(true);
        try {
          const res = await systemDatametaAPI[api](formDetail.formDataDraftId, {
            message,
            postId: receivePostId,
          });
          if (formLists.length > 1) {
            findFormList(bizParam.approveIds?.filter((id) => id !== formDetail.formDataDraftId));
          } else {
            bridge.close();
          }
        } catch (e) {
          setLoading(false);
        }
      };
      geneModal(`确定${tittle}吗？`, excuteFn);
    } catch (e) {}
  };

  const renderItems = () => {
    const mainDataUnit = formVersion?.dataUnits?.find((item) => item.mainDataUnit);
    return cFields?.map((formField, index) => {
      const uniqueId = getMetaTagUniqueId(formField);
      const colSpan = formField?.widgetWith === '100%' ? 24 : 12;
      const renderTag = () => {
        const tag = cTagsMap[uniqueId];
        console.log(tag, 'tag---------');
        if (!tag) {
          return null;
        }
        const record = formDetail?.draftItems?.find((item) => item.dataUnitId === mainDataUnit?.dataUnitId)?.record;
        if (!record) {
          return null;
        }
        const renderCell = () => {
          const type = tag?.tagMetaDataConfig?.type;
          const codeKey = formField?.dataUnitId === mainDataUnit?.dataUnitId ? formField.code : `${formField.dataUnitId}_${formField.code}`;
          const value = record[codeKey];
          switch (type) {
            case MetaDataType.Text: {
              return <TextMeta tag={tag} field={formField} value={value} />;
            }
            case MetaDataType.Number: {
              return <NumberMeta tag={tag} field={formField} value={value} />;
            }
            case MetaDataType.DateTime: {
              return <TimeMeta tag={tag} field={formField} value={value} />;
            }
            case MetaDataType.Object: {
              return <ObjectMeta tag={tag} field={formField} value={value} />;
            }
            case MetaDataType.Unit: {
              return <UnitMeta tag={tag} field={formField} value={value} />;
            }
            case MetaDataType.Code: {
              return <CodeMeta tag={tag} field={formField} value={value} />;
            }
            case MetaDataType.File: {
              return <FileMeta tag={tag} field={formField} value={value} />;
            }
            case MetaDataType.Address: {
              return <AddressMeta tag={tag} field={formField} value={value} />;
            }
            default: {
              return null;
            }
          }
        };

        return (
          <Form.Item
            label={
              <div className={styles.labelWrapper}>
                <span>
                  <span className={styles.labelName}>{formField.fieldName || tag.name}</span>
                  {formField.fieldName && (
                    <Tooltip title={`原标签名称：${tag.name}`}>
                      <InfoCircleOutlined style={{ marginLeft: 5, color: '#4D7BF6' }} />
                    </Tooltip>
                  )}
                </span>
              </div>
            }
          >
            {renderCell()}
          </Form.Item>
        );
      };

      const renderCombineField = () => {
        return (
          <>
            <Form.Item
              label={
                <div className={styles.labelWrapper}>
                  <span>{formField.combineConfig?.name}</span>
                </div>
              }
            >
              <span>回显数据</span>
            </Form.Item>
          </>
        );
      };

      return (
        <Col span={colSpan} style={{ flexShrink: 0 }} key={index}>
          <div key={uniqueId} className={styles.formItemWrapper}>
            {formField.sourceType === SourceType.Tag && renderTag()}
            {formField.sourceType === SourceType.Field && renderCombineField()}
          </div>
        </Col>
      );
    });
  };

  const renderDynamincForm = () => {
    return (
      <Form layout="vertical" form={form} className={styles.dynamincFormStyle}>
        {/* <Form.Item label="名称" name="tagList">  */}
        <Row gutter={24}>{renderItems()}</Row>
        {/* {renderItems()} */}
        <Form.Item
          name="message"
          label={<span className={styles.tagLabel}>审核意见</span>}
          layout="vertical"
          rules={[{ required: true, message: '请输入审核意见' }]}
        >
          <Input.TextArea
            placeholder="请输入审核意见"
            autoSize={{
              minRows: 3,
              maxRows: 3,
            }}
            style={{ marginTop: '12px' }}
          />
        </Form.Item>
      </Form>
    );
  };

  return (
    <LayoutPage
      loading={loading}
      footer={false}
      header={{ title: '表单管理' }}
      contentClassName={styles.layoutContent}
      onCancel={() => bridge.close()}
    >
      <div className={styles.contentWrapper}>
        <div className={styles.leftContent}>
          <div className={styles.leftTop}>
            <Input.Search
              prefix={<SearchOutlined style={{ color: '#909296' }} />}
              placeholder={`搜索表单名称`}
              allowClear
              onSearch={(e) => setSearchValue(e.trim())}
            />
            <div className={styles.tabTitle}>表单列表（{formLists?.length}）</div>
          </div>
          <div className={styles.formList}>
            {formLists
              .filter((item) => (item.name || '').includes(searchValue))
              .map((item) => (
                <div
                  key={item?.formDataDraftId}
                  className={classNames(styles.formStyle, {
                    [styles.active]: item.formDataDraftId === formDetail?.formDataDraftId,
                  })}
                  onClick={() => {
                    setFormDetail(item);
                    getFormDetail(item.formDataDraftId);
                  }}
                >
                  <div style={{ display: 'flex', marginBottom: '5px', columnGap: 5 }}>
                    <img src={IconFormExcute} width={24} height={24} />
                    <div className={styles.formName}>
                      <TextEllipsisTooltip text={item?.name} />
                    </div>
                  </div>
                  <div className={styles.formDesc}>
                    <span style={{ marginRight: '5px' }}>{`${item?.creatorPost}/${item?.creatorUser}`}</span>
                    <span style={{ marginLeft: '5px' }}>{formatDateTime(item?.createTime)}</span>
                  </div>
                </div>
              ))}
          </div>
        </div>
        <div key={formDetail?.formDataDraftId} className={styles.rightForm}>
          <div className={styles.formContent}>
            <div className={styles.formTitle}>
              <div>
                <span className={styles.titleLabel}>表单内容</span>
                <span>(发起时间：{formatDateTime(formDetail?.createTime)})</span>
              </div>
              <div
                style={{ color: '#5884f6' }}
                onClick={() => {
                  setPreviewModal({
                    open: true,
                    url: formDetail?.wordFileUrl,
                  });
                }}
              >
                查看表单 <DoubleRightOutlined />
              </div>
            </div>
            <div className={styles.renderItems}>{renderDynamincForm()}</div>
            {!isEmpty(formVersion?.extConfig?.combineTagConfig) && (
              <>
                <div>组合标签</div>
                <TableAndCombine
                  formDataDraftId={formDetail?.formDataDraftId}
                  cTagsMap={cTagsMap}
                  records={formDetail?.draftItems}
                  combineConfig={formVersion?.extConfig?.combineTagConfig}
                  dataUnits={formVersion?.dataUnits}
                />
              </>
            )}
            {tableList?.length > 0 && (
              <>
                <div>数据表格</div>
                <div>
                  {tableList?.length > 0 &&
                    tableList.map((tabelItem, tableIndex) => (
                      <div key={tableIndex}>
                        <TableAndCombine formDataDraftId={formDetail?.formDataDraftId} tableConfig={tabelItem} cTagsMap={cTagsMap} />
                      </div>
                    ))}
                </div>
              </>
            )}
          </div>
          <div className={styles.rightBottom}>
            <Button type="primary" ghost onClick={() => handleStatus('back')}>
              退回
            </Button>
            <Button danger onClick={() => handleStatus('reject')}>
              拒绝
            </Button>
            <Button type="primary" onClick={() => handleAgree()}>
              同意
            </Button>
          </div>
        </div>
      </div>

      {/* 表单预览 */}
      {previewModal.open && <EditorPreview fileUrl={previewModal.url} onClose={() => setPreviewModal({ open: false })} />}
    </LayoutPage>
  );
});

export default SystemDataMetaApprove;
