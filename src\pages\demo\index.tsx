import ExecutionEventModel from '../app-apply/components/app-form/execution-event-model';

const btnItem = {
  operateType: 'LAUNCH',
  buttonStyle: 'primary',
  events: [
    {
      eventType: 'POST_ENTRY',
      params: [],
      postEntryOrLevelConfig: {
        postEntryTypeList: ['SELECT_EXIST', 'AUTO_CREATE'],
        toUser: {
          dataUnitId: 250,
          tagId: 2399,
        },
        toPost: {
          dataUnitId: 250,
          tagId: 2599,
        },
        toWorkType: {
          dataUnitId: 250,
          tagId: 2401,
        },
        toContractType: {
          dataUnitId: 250,
          tagId: 2401,
        },
        toEntryTime: {
          dataUnitId: 250,
          tagId: 2400,
        },
      },
    },
  ],
};

const Demo = () => {
  return (
    <ExecutionEventModel
      btnItem={btnItem}
      handelCancel={() => {}}
      handleOk={(values) => {
        console.log(values);
      }}
    />
  );
};

export default Demo;
