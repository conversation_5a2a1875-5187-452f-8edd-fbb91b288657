import { CloseCircleFilled, CopyOutlined, DeleteOutlined, PlusCircleOutlined, QuestionCircleOutlined } from '@ant-design/icons';
import { Form, message, Select, Switch, Tooltip } from '@gwy/components-web';
import { cloneDeep } from 'lodash';
import { useEffect, useMemo } from 'react';
import ConditionGroupRemark from './components/condition-group-remark';
import RenderDynamicItem from './components/render-item';
import RenderSpecialItem from './components/render-special-item';
import { DATA_TYPE, GROUP_LOGIC_OPTIONS, GROUP_LOGIC_TYPE, NEW_RELATION_TYPE } from './filter-const';
import styles from './index.less';

interface Iprops {
  disabled?: boolean;
  prefixNames?: any[];
  conditionGroups?: any;
  tags: any[];
  canRelateTags: any[];
  calcFields?: any[];
  useInStage?: UseInStageEnum;
  orderStart?: number;
}
export enum UseInStageEnum {
  DataSync = 'DataSync',
  DynamicParams = 'DynamicParams',
}
const FilterGroup = (props: Iprops) => {
  const { disabled = false, prefixNames = [], conditionGroups, tags, canRelateTags, calcFields = [], useInStage, orderStart = 0 } = props;
  const form = Form.useFormInstance();
  const _conditionGroups = Form.useWatch([...prefixNames, 'conditionGroups'], form);
  const hasPreCondition = _conditionGroups?.[0]?.preCondition; // 是否有公共条件组
  const usedTags = useMemo(() => {
    // 过滤掉文件和地址类型的标签
    const filterTags = tags.filter((tag) => ![DATA_TYPE.FILE?.val, DATA_TYPE.ADDRESS?.val].includes(tag.tagMetaDataConfig?.type));
    return filterTags.map((tag) => ({
      ...tag,
      label: tag.name,
      value: tag.tagId,
    }));
  }, [tags]);
  const isSpecialTag = (tag) => {
    return tag?.type === 'SPECIAL' && tag?.tagId === -10;
  };

  const getRelationOps = (tagId) => {
    if (!tagId) return [];
    const tag = tags.find((item) => item.tagId === tagId);
    const { tagMetaDataConfig } = tag || {};
    const { type } = tagMetaDataConfig || {};
    // 特殊标签处理-【处理情况】标签
    if (isSpecialTag(tag)) {
      return (DATA_TYPE.SPECIAL?.relation || []).map((rela) => ({
        ...rela,
        label: rela.text,
        value: rela.val,
      }));
    }
    switch (type) {
      case DATA_TYPE.TEXT.val: {
        const { metaDataTextDTO } = tagMetaDataConfig || {};
        const dataType = metaDataTextDTO?.inputType === 'MANUAL' ? DATA_TYPE.TEXT?.val : DATA_TYPE.TEXTENUM?.val;
        return (DATA_TYPE[dataType]?.relation || []).map((rela) => ({
          ...rela,
          label: rela.text,
          value: rela.val,
        }));
      }
      case DATA_TYPE.OBJECT.val: {
        const { metaDataObjectDTO } = tagMetaDataConfig || {};
        const { configList } = metaDataObjectDTO || {};
        const relations = (DATA_TYPE[type]?.relation || []).map((rela) => ({
          ...rela,
          label: rela.text,
          value: rela.val,
        }));

        if (configList?.length > 0) {
          return relations.concat({
            ...NEW_RELATION_TYPE.DYNAMIC_RANGE,
            label: NEW_RELATION_TYPE.DYNAMIC_RANGE.text,
            value: NEW_RELATION_TYPE.DYNAMIC_RANGE.val,
          });
        }
        return relations;
      }
      default: {
        return (DATA_TYPE[type]?.relation || []).map((rela) => ({
          ...rela,
          label: rela.text,
          value: rela.val,
        }));
      }
    }
  };
  const getCanuseRelateTags = (tag) => {
    const { tagMetaDataConfig, tagId } = tag || {};
    const { type } = tagMetaDataConfig || {};
    return (canRelateTags || [])
      .filter((tag) => tag?.tagMetaDataConfig?.type === type && tagId !== tag.tagId)
      .map((tag) => ({
        ...tag,
        label: tag.name,
        value: tag.tagId,
      }));
  };

  const renderGroupTitle = (name) => {
    const currentPreCondition = form.getFieldValue([...prefixNames, 'conditionGroups', name, 'preCondition']);

    if (hasPreCondition) {
      if (currentPreCondition) {
        return '公共条件组';
      }
      return `条件组${name}`;
    }
    return `条件组${name + 1}`;
  };

  const renderGroupLogic = (name, index, fields, disabled = false) => {
    if (index === 0) {
      return null;
    }
    return (
      <div>
        <Form.Item
          name={[name, 'groupLogic']}
          style={{ display: hasPreCondition && index === 1 ? 'none' : 'block', padding: '10px 0px', marginBottom: 0 }}
          initialValue={GROUP_LOGIC_TYPE.AND}
        >
          <Select
            placeholder="请选择"
            style={{ width: 'fit-content' }}
            allowClear={false}
            disabled={disabled}
            onSelect={(value) => {
              // 将所有条件组里的groupLogic设置为value
              const fieldGroupList = form.getFieldValue([...prefixNames, 'conditionGroups']) || [];
              fieldGroupList.forEach((item, i) => {
                Object.assign(item, { groupLogic: hasPreCondition && i === 0 ? GROUP_LOGIC_TYPE.AND : value });
              });
              form.setFieldValue([...prefixNames, 'conditionGroups'], fieldGroupList);
            }}
          >
            {GROUP_LOGIC_OPTIONS.map((item) => (
              <Select.Option key={item.value} value={item.value}>
                {item.label}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>
      </div>
    );
  };

  const renderAddGroup = (add, disabled = false) => {
    if (disabled) {
      return null;
    }
    if (tags.length === 0) {
      return (
        <div className={styles.addBtnWrapper}>
          <span>请先选择数据单元</span>
        </div>
      );
    }
    return (
      <div className={styles.addGroupBtn} style={{ margin: '10px auto' }}>
        <span
          onClick={() =>
            add({
              groupLogic: _conditionGroups?.[0]?.groupLogic || GROUP_LOGIC_TYPE.AND,
            })
          }
        >
          <PlusCircleOutlined />
          <span>添加条件组</span>
        </span>
      </div>
    );
  };

  const renderFieldItem = (field, index, fields, remove, disabled = false) => {
    const _conditionItems = _conditionGroups?.[field.name]?.conditionItems || [];
    return (
      <div key={field.key} className={styles.itemWrapper}>
        {/* 条件组之间的逻辑关系控件 */}
        {renderGroupLogic(field.name, index, fields, disabled)}
        <div className={styles.itemContent}>
          <div className={styles.groupTop}>
            <div className={styles.left}>
              <Form.Item shouldUpdate noStyle>
                {() => {
                  const remark = form.getFieldValue([...prefixNames, 'conditionGroups', field.name, 'remark']);
                  return (
                    <div className={styles.groupTitle}>
                      {renderGroupTitle(field.name)}
                      {remark && (
                        <Tooltip title={remark}>
                          <QuestionCircleOutlined style={{ marginLeft: 8 }} />
                        </Tooltip>
                      )}
                    </div>
                  );
                }}
              </Form.Item>
            </div>
            {!disabled && (
              <div className={styles.right}>
                {/* 公共条件组 */}
                {index === 0 && (
                  <Form.Item name={[field.name, 'preCondition']} label="公共条件组" style={{ marginBottom: 0, marginRight: 30 }}>
                    <Switch
                      onChange={(hasPreCondition) => {
                        const fieldGroupList = form.getFieldValue([...prefixNames, 'conditionGroups']) || [];
                        Object.assign(fieldGroupList[0], {
                          groupLogic: hasPreCondition ? GROUP_LOGIC_TYPE.AND : fieldGroupList[1]?.groupLogic ?? GROUP_LOGIC_TYPE.AND,
                        });
                        form.setFieldValue([...prefixNames, 'conditionGroups'], fieldGroupList);
                      }}
                    />
                  </Form.Item>
                )}
                {/* 备注 */}
                <Form.Item name={[field.name, 'remark']} noStyle>
                  <ConditionGroupRemark />
                </Form.Item>
                {/* 复制条件组 */}
                <Tooltip title="复制">
                  <CopyOutlined
                    className={styles.iconCopyGroup}
                    onClick={() => {
                      // 复制当前条件组
                      const fieldGroupList = form.getFieldValue([...prefixNames, 'conditionGroups']) || [];
                      const currentGroup = fieldGroupList[field.name];
                      if (currentGroup) {
                        // 深拷贝
                        const copiedGroup = cloneDeep(currentGroup);
                        fieldGroupList.splice(field.name + 1, 0, copiedGroup);
                        form.setFieldValue([...prefixNames, 'conditionGroups'], fieldGroupList);
                      }
                      message.success('复制成功');
                    }}
                  />
                </Tooltip>
                {/* 删除 */}
                <DeleteOutlined style={{ fontSize: 16, cursor: 'pointer' }} onClick={() => remove(index)} />
              </div>
            )}
          </div>
          <Form.List name={[field.name, 'conditionItems']} initialValue={[{}]}>
            {(fieldItems, { add: addItem, remove: removeItem }) =>
              fieldItems.map((fieldItem, idx) => {
                const _conditionItem = _conditionItems?.[fieldItem.name];
                const _enabled = _conditionItem?._enabled;
                return (
                  <div key={idx}>
                    <div style={{ display: 'flex', alignItems: 'baseline', justifyContent: 'flex-start', columnGap: '15px', width: '100%' }}>
                      <div>{idx === 0 ? '当' : '且'}</div>
                      <Form.Item name={[fieldItem.name, 'tagId']} style={{ width: '120px' }} rules={[{ required: true, message: '请选择标签' }]}>
                        <Select
                          placeholder="请选择标签"
                          disabled={disabled}
                          options={usedTags}
                          onChange={(value) => {
                            // 优化：构造公共路径，使用一次批量 setFields 更新，减少多次赋值与重复路径拼接
                            const tag = usedTags.find((item) => item.value === value);
                            const baseName = [...prefixNames, 'conditionGroups', field.name, 'conditionItems', fieldItem.name];
                            form.setFields([
                              { name: [...baseName, 'type'], value: tag?.tagMetaDataConfig?.type },
                              { name: [...baseName, 'code'], value: tag?.code },
                              { name: [...baseName, 'value'], value: undefined },
                              { name: [...baseName, 'operator'], value: undefined },
                              { name: [...baseName, 'valueType'], value: undefined },
                            ]);
                          }}
                        />
                      </Form.Item>
                      <Form.Item name="type" hidden />
                      <Form.Item name="code" hidden />
                      <Form.Item name={[fieldItem.name, 'itemLogic']} initialValue="AND" hidden />
                      <Form.Item shouldUpdate>
                        {() => {
                          const currentTagId = form.getFieldValue([
                            ...prefixNames,
                            'conditionGroups',
                            field.name,
                            'conditionItems',
                            fieldItem.name,
                            'tagId',
                          ]);
                          const relaOps = getRelationOps(currentTagId);
                          return (
                            <Form.Item name={[fieldItem.name, 'operator']} style={{ width: '100px' }} rules={[{ required: true, message: '请选择' }]}>
                              <Select
                                placeholder="请选择"
                                disabled={disabled}
                                options={relaOps}
                                onChange={() => {
                                  const baseName = [...prefixNames, 'conditionGroups', field.name, 'conditionItems', fieldItem.name];
                                  form.setFields([
                                    { name: [...baseName, 'value'], value: undefined },
                                    { name: [...baseName, 'valueType'], value: undefined },
                                  ]);
                                }}
                              />
                            </Form.Item>
                          );
                        }}
                      </Form.Item>
                      <Form.Item shouldUpdate noStyle>
                        {() => {
                          const currentTagId = form.getFieldValue([
                            ...prefixNames,
                            'conditionGroups',
                            field.name,
                            'conditionItems',
                            fieldItem.name,
                            'tagId',
                          ]);
                          const tag = tags.find((item) => item.tagId === currentTagId);
                          const isSpecial = isSpecialTag(tag);
                          const currentRelation = form.getFieldValue([
                            ...prefixNames,
                            'conditionGroups',
                            field.name,
                            'conditionItems',
                            fieldItem.name,
                            'operator',
                          ]);
                          const canRelatedTags = getCanuseRelateTags(tag);
                          return isSpecial ? (
                            <RenderSpecialItem
                              form={form}
                              disabled={disabled}
                              prefix={[fieldItem.name]}
                              parenntPrefix={[...prefixNames, 'conditionGroups', field.name, 'conditionItems', fieldItem.name]}
                            />
                          ) : (
                            <RenderDynamicItem
                              form={form}
                              disabled={disabled && !_enabled}
                              prefix={[fieldItem.name]}
                              currentTag={tag}
                              tags={tags}
                              relation={currentRelation}
                              parenntPrefix={[...prefixNames, 'conditionGroups', field.name, 'conditionItems', fieldItem.name]}
                              canRelatedTags={canRelatedTags}
                              calcFields={calcFields}
                              useInStage={useInStage}
                            />
                          );
                        }}
                      </Form.Item>
                      {!disabled && (
                        <span
                          className={styles.deleteBtn}
                          onClick={() => {
                            if (fieldItems.length - 1 === 0) {
                              return;
                            }
                            removeItem(idx);
                          }}
                        >
                          <CloseCircleFilled style={{ fontSize: 16 }} />
                        </span>
                      )}
                    </div>
                    {idx === fieldItems.length - 1 && !disabled && (
                      <span className={styles.addBtn} onClick={() => addItem()} style={{ marginLeft: '-5px' }}>
                        <PlusCircleOutlined />
                        <span>添加条件</span>
                      </span>
                    )}
                  </div>
                );
              })
            }
          </Form.List>
          <Form.Item name={[field.name, 'order']} hidden initialValue={index + orderStart} />
        </div>
      </div>
    );
  };

  const renderConditionGroup = (fields, add, remove, disabled = false) => {
    if (hasPreCondition) {
      return (
        <>
          {renderFieldItem(fields[0], 0, fields, remove, disabled)}
          {fields.length > 1 && (
            <div className={styles.conditionInnerWrapper}>
              <div className={styles.logicText}>且</div>
              {fields.map((field, index) => {
                if (index === 0) {
                  return null;
                }
                return renderFieldItem(field, index, fields, remove, disabled);
              })}
            </div>
          )}
          {/* 添加条件组 */}
          {renderAddGroup(add, disabled)}
        </>
      );
    } else {
      return (
        <div>
          {fields.map((field, index) => {
            return renderFieldItem(field, index, fields, remove, disabled);
          })}
          {/* 添加条件组 */}
          {renderAddGroup(add, disabled)}
        </div>
      );
    }
  };

  useEffect(() => {
    form?.resetFields();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    if (conditionGroups) {
      form.setFieldValue([...prefixNames, 'conditionGroups'], conditionGroups);
    }
  }, [conditionGroups, form]);
  return (
    <Form.List name={[...prefixNames, 'conditionGroups']}>
      {(fields, { add, remove }) => {
        return <>{fields.length === 0 ? renderAddGroup(add, disabled) : renderConditionGroup(fields, add, remove, disabled)}</>;
      }}
    </Form.List>
  );
};

export default FilterGroup;
