import FilterGroup from '@/components/filter-group';
import { getFormVersion } from '@/services/app';
import { getDepartmentPostList } from '@/services/common';
import { getOrgDataUnitsTags } from '@/services/org-data-unit';
import { FormVO } from '@/types/app';
import { Post } from '@/types/post';
import { PlusCircleOutlined, QuestionCircleOutlined } from '@ant-design/icons';
import { Form, Modal, Radio, Tooltip } from '@gwy/components-web';
import { isEmpty } from 'lodash-es';
import { memo, useCallback, useEffect, useState } from 'react';
import AuthorizeItem from './components/authorize-item';
import FormItemSwitch from './components/form-item-switch';
import styles from './index.less';

interface IProps {
  post: Post;
  formData: any;
  onOk?: () => void;
  onCancel?: () => void;
}

const layout = {
  labelCol: { span: 5 },
  wrapperCol: { span: 19 },
};

const handleDataUnitsToTags = (dataUnits) => {
  let results = [];

  dataUnits.forEach((dataUnit) => {
    if (dataUnit?.tagList?.length > 0) {
      results.push(
        ...(dataUnit.tagList || []).map((tag) => {
          return {
            ...tag,
            belongDataUnitId: dataUnit.dataUnitId,
            belongDataUnitName: dataUnit.name,
          };
        }),
      );
    }
  });
  return results;
};

const FieldsLibsModal = memo<IProps>(({ post, formData, onOk, onCancel }) => {
  const [form] = Form.useForm();
  const [filterForm] = Form.useForm();
  const [tags, setTags] = useState([]);
  const authorize = Form.useWatch('authorize', form);
  const [filterOpen, setFilterOpen] = useState({
    open: false,
    prefixNames: [],
    conditionGroups: [],
  });
  const [posts, setPosts] = useState([]);

  const getDepartmentPostListData = useCallback(async () => {
    const posts = await getDepartmentPostList({ departmentId: post?.departmentId });

    setPosts(posts);
  }, [post]);

  const getTagsByDataUnitIds = useCallback(
    async (dataUnitIds) => {
      const dataUnits = await getOrgDataUnitsTags({ orgId: post?.orgId, dataUnitIds });
      const tags = handleDataUnitsToTags(dataUnits);

      setTags(tags);
    },
    [post?.orgId],
  );

  const getFormInfo = useCallback(
    async (formVersionId) => {
      const data: FormVO = await getFormVersion(formVersionId);
      const { dataUnits, dataSubmitCheckConfig = {} } = data;
      const { autoPass = false, authorize = false, conditionGroups = [], authorizePostConfs = [] } = dataSubmitCheckConfig || {};
      const configuredDataUnits = !isEmpty(dataUnits) ? dataUnits : [{}];

      getTagsByDataUnitIds(configuredDataUnits.map((item) => item.dataUnitId));
      form.setFieldsValue({
        autoPass,
        authorize,
        conditionGroups,
        authorizePostConfs,
      });
    },
    [getTagsByDataUnitIds, form],
  );

  const renderAddItem = (add) => {
    return (
      <div className={styles.addAuthorizeBtn} style={{ margin: '10px auto' }}>
        <span onClick={() => add()}>
          <PlusCircleOutlined />
          <span>添加授权</span>
        </span>
      </div>
    );
  };

  const renderAuthorizeGroup = (fields, add, remove) => {
    return (
      <div>
        {fields.map((field, index) => {
          return (
            <AuthorizeItem
              key={field.key}
              field={field}
              index={index}
              posts={posts}
              setConditionGroup={() => {
                const prefixNames = ['authorizePostConfs', field.name, 'conditionGroups'];
                const conditionGroups = form.getFieldValue([...prefixNames]);

                setFilterOpen({
                  open: true,
                  prefixNames: prefixNames,
                  conditionGroups: conditionGroups,
                });
              }}
              onRemove={() => remove(index)}
            />
          );
        })}
        {/* 添加授权 */}
        {renderAddItem(add)}
      </div>
    );
  };

  useEffect(() => {
    getFormInfo(formData.formVersionId);
    getDepartmentPostListData();
  }, [formData.formVersionId, getFormInfo, getDepartmentPostListData]);

  return (
    <Modal
      open
      title="核准配置"
      okText="确定"
      onOk={() => onOk()}
      onCancel={onCancel}
      width={600}
      styles={{
        body: {
          height: 672,
        },
      }}
    >
      <div className={styles.container}>
        <Form {...layout} form={form}>
          <div className={styles.top}>
            <span className={styles.label}>已选表单: </span>
            <span className={styles.formName}>{formData.name}</span>
          </div>
          <div className={styles.content}>
            <div className={styles.title}>数据核准配置</div>
            <Form.Item hidden name="conditionGroups" />
            <Form.Item
              name="autoPass"
              label={
                <span className={styles.label}>
                  审批自动通过
                  <Tooltip title="开启后，录入的数据若满足筛选条件，则核准审批将会自动通过。">
                    <QuestionCircleOutlined className={styles.icon} />
                  </Tooltip>
                </span>
              }
            >
              <FormItemSwitch
                onEdit={() => {
                  const prefixNames = ['conditionGroups'];
                  const conditionGroups = form.getFieldValue(prefixNames);
                  setFilterOpen({
                    open: true,
                    prefixNames,
                    conditionGroups,
                  });
                }}
              />
            </Form.Item>
            <Form.Item
              name="authorize"
              initialValue={false}
              label={
                <span className={styles.label}>
                  审批授权
                  <Tooltip title="管理岗可将自己的核准审批权限授给其他岗位。">
                    <QuestionCircleOutlined className={styles.icon} />
                  </Tooltip>
                </span>
              }
            >
              <Radio.Group>
                <Radio value={false}>不授权</Radio>
                <Radio value={true}>授权</Radio>
              </Radio.Group>
            </Form.Item>
            {authorize && (
              <Form.List name="authorizePostConfs">
                {(fields, { add, remove }) => {
                  return renderAuthorizeGroup(fields, add, remove);
                }}
              </Form.List>
            )}
          </div>
        </Form>
        {filterOpen.open && (
          <Modal
            title="配置条件"
            open={true}
            size="middle"
            onCancel={() => {
              setFilterOpen({
                open: false,
                prefixNames: [],
                conditionGroups: [],
              });
            }}
            onOk={() => {
              const values = filterForm.getFieldsValue();
              form.setFieldValue([...filterOpen.prefixNames], values?.conditionGroups);
              setFilterOpen({
                open: false,
                prefixNames: [],
                conditionGroups: [],
              });
            }}
          >
            <Form form={filterForm}>
              <FilterGroup tags={tags} canRelateTags={tags} conditionGroups={filterOpen.conditionGroups} />
            </Form>
          </Modal>
        )}
      </div>
    </Modal>
  );
});

export default FieldsLibsModal;
