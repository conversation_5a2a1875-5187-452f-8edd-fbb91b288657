import { RangeType } from '@/const/metadata';
import { commonAPI } from '@/services';
import { FormField } from '@/types/form-field';
import { DataUnitTagVO } from '@/types/tag';
import { genUuid } from '@/utils';
import { centerAndZoom, closeInfoWindow, createMap, enableScrollWheelZoom, initPoint, loadMap, openInfoWindow } from '@/utils/map';
import { EnvironmentOutlined } from '@ant-design/icons';
import { Input, message, Modal } from '@gwy/components-web';
import { memo, useEffect, useRef, useState } from 'react';
import { addressRangeCount, loopTreeRange, treeFilter } from '../area';

const mapDefaultCenter = {
  lon: 120.071937,
  lat: 30.317501,
};

const geocoder = new BMap.Geocoder();

type IValue = {
  regionName: string;
  cityName: string;
  provinceName: string;
  detail: string;
  longitude: number;
  latitude: number;
};
const MapItem = memo<{
  tag?: DataUnitTagVO;
  field?: FormField;
  value?: IValue[];
  onChange?: (value?: IValue[]) => void;
}>(({ value, onChange, field, tag }) => {
  const mapRef = useRef<any>();
  const localRef = useRef({
    mapId: genUuid(),
    infoWin: null,
  });

  const { metaDataAddressDTO } = tag.tagMetaDataConfig || {};
  const { codeList, rangeType } = metaDataAddressDTO || {};

  const { config, placeholder } = field || {};
  const { addressRange } = config || {};

  const [point, setPoint] = useState<IValue>(value?.[0]);

  const [open, setOpen] = useState(false);
  const [isInit, setIsInit] = useState(false);

  /**
   * ('省' | '省市' | '省市区')[]
   */
  const [cascaders, setCascaders] = useState<string[]>([]);

  const { detail } = value?.[0] || {};

  useEffect(() => {
    setPoint(value?.[0]);
  }, [value]);

  useEffect(() => {
    (async () => {
      const data = await commonAPI.getAreaTree();
      if (Array.isArray(data) && data.length > 0) {
        const rangeTree = loopTreeRange(data, addressRangeCount[addressRange]);
        let filterTree = rangeTree;
        if (rangeType !== RangeType.unlimited) {
          filterTree = treeFilter(filterTree, codeList, rangeType);
        }
        const cascaders = [];

        filterTree.forEach((p) => {
          if (p.children && p.children.length > 0) {
            p.children.forEach((c) => {
              if (c.children && c.children.length > 0) {
                c.children.forEach((r) => {
                  cascaders.push(`${p.name}${c.name}${r.name}`);
                });
              } else {
                cascaders.push(`${p.name}${c.name}`);
              }
            });
          } else {
            cascaders.push(p.name);
          }
        });

        setCascaders(cascaders);
      }
    })();
  }, [addressRange, rangeType, codeList]);

  useEffect(() => {
    const onClick = (e) => {
      geocoder.getLocation(e.point, (aa) => {
        const { address, addressComponents, point } = aa;
        const pointArea = `${addressComponents.province || ''}${addressComponents.city || ''}${addressComponents.district || ''}`;
        if (!cascaders.some((area) => pointArea.includes(area))) {
          return message.warning('不可选取当前区域');
        }
        setPoint({
          provinceName: addressComponents.province,
          cityName: addressComponents.city,
          regionName: addressComponents.district,
          detail: address,
          longitude: point.lng,
          latitude: point.lat,
        });
      });
    };
    if (isInit) {
      setTimeout(() => {
        loadMap(() => {
          const map = createMap(localRef.current.mapId, { enableMapClick: true });
          if (map) {
            mapRef.current = map;

            enableScrollWheelZoom(map);
            centerAndZoom(map, initPoint(mapDefaultCenter.lon, mapDefaultCenter.lat), 18);

            mapRef.current = map;

            map.addEventListener('click', onClick);
          }
        });
      }, 100);
    }
    return () => {
      if (mapRef.current) {
        mapRef.current.removeEventListener('click', onClick);
      }
    };
  }, [isInit, cascaders]);

  useEffect(() => {
    localRef.current.infoWin && mapRef.current && closeInfoWindow(mapRef.current, localRef.current.infoWin);

    if (point?.longitude) {
      const infoWin = openInfoWindow(
        mapRef.current,
        point.detail,
        {
          width: 250, // 信息窗口宽度
          height: 80, // 信息窗口高度
          title: '地点信息', // 信息窗口标题
        },
        new BMap.Point(point.longitude, point.latitude),
      );
      localRef.current.infoWin = infoWin;
    }
  }, [point]);
  return (
    <div>
      <Input
        value={detail}
        addonAfter={<EnvironmentOutlined />}
        onClick={() => {
          !isInit && setIsInit(true);
          setOpen(true);
        }}
        placeholder={placeholder || '请选择'}
        allowClear
        readOnly
      />
      <Modal
        open={open}
        title={false}
        closable={false}
        width={600}
        size="small"
        onOk={() => {
          if (!point) {
            return message.error('请选择地址');
          }
          onChange([point]);
          setOpen(false);
        }}
        onCancel={() => setOpen(false)}
        destroyOnHidden={false}
      >
        <div id={localRef.current.mapId} style={{ height: '100%' }} />
      </Modal>
    </div>
  );
});

export default MapItem;
