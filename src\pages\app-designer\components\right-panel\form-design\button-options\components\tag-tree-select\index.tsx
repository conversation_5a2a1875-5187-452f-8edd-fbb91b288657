import { TreeSelect } from '@gwy/components-web';

export const getTagValue = (tag: ValueType) => `${tag.dataUnitId}_${tag.tagId}`;

type ValueType = {
  dataUnitId?: number;
  tagId?: number;
};

type Props = {
  style?: React.CSSProperties;
  treeData?: any[];
  value?: ValueType;
  onChange?: (value: ValueType) => void;
};

const TagTreeSelect = ({ style, treeData, value, onChange }: Props) => {
  return (
    <TreeSelect
      style={style}
      styles={{
        popup: {
          root: { maxHeight: 400, overflow: 'auto' },
        },
      }}
      virtual={false}
      placeholder="请选择"
      allowClear
      showSearch
      treeDefaultExpandAll
      treeData={treeData}
      value={value ? getTagValue(value) : undefined}
      onChange={(v) => {
        if (!v) {
          onChange?.(undefined);
          return;
        }
        const [dataUnitId, tagId] = v.split('_');
        onChange?.({ dataUnitId: Number(dataUnitId), tagId: Number(tagId) });
      }}
    />
  );
};

export default TagTreeSelect;
