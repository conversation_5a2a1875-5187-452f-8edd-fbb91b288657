/* 授权管理弹窗 */
import { QuestionCircleOutlined } from '@ant-design/icons';
import { Button, Checkbox, Modal, Radio, Select, Tooltip, message } from '@gwy/components-web';
import { useEffect, useState } from 'react';
import { departments as initialDepartments } from './const';
import styles from './index.less';
import PositionSelector from './position-selector';

interface IProps {
  selectedForms: any[];
  onOk: (values: any) => void;
  onCancel: () => void;
}

const AuthorizationManagement = ({ onCancel, onOk, selectedForms = [{ id: '1', name: '表单名称1' }] }: IProps) => {
  const [currentSelectedForms, setCurrentSelectedForms] = useState<any[]>(selectedForms);
  const [authorizationType, setAuthorizationType] = useState<'all' | 'type' | 'specific'>('all');
  const [functionPermissions, setFunctionPermissions] = useState<any>({
    formUsage: true,
    formAuthorization: true,
    topLevelAuthorization: false,
  });
  const [doNotDisturb, setDoNotDisturb] = useState(true);
  const [selectedPositions, setSelectedPositions] = useState<any[]>([]);
  const [departments, setDepartments] = useState<any[]>([]);

  // 初始化部门数据
  useEffect(() => {
    setDepartments(JSON.parse(JSON.stringify(initialDepartments)));
  }, []);

  // 计算部门的选中状态和半选状态
  const getDepartmentCheckState = (dept: any) => {
    const checkedCount = dept.positionTypes.filter((pt: any) => pt.checked).length;
    const totalCount = dept.positionTypes.length;

    if (checkedCount === 0) {
      return { checked: false, indeterminate: false };
    } else if (checkedCount === totalCount) {
      return { checked: true, indeterminate: false };
    } else {
      return { checked: false, indeterminate: true };
    }
  };

  const handleAuthorizationTypeChange = (value: 'all' | 'type' | 'specific') => {
    setAuthorizationType(value);
    if (value === 'all') {
      setSelectedPositions([]);
    }
  };

  const handleFunctionPermissionChange = (key: string, checked: boolean) => {
    setFunctionPermissions((prev) => ({
      ...prev,
      [key]: checked,
    }));
  };

  const handleDepartmentChange = (departmentId: string, checked: boolean) => {
    setDepartments((prev) =>
      prev.map((dept) =>
        dept.id === departmentId
          ? {
              ...dept,
              checked,
              positionTypes: dept.positionTypes.map((pt) => ({ ...pt, checked })),
            }
          : dept,
      ),
    );
  };

  const handlePositionTypeChange = (departmentId: string, positionTypeId: string, checked: boolean) => {
    setDepartments((prev) =>
      prev.map((dept) => {
        if (dept.id === departmentId) {
          const updatedPositionTypes = dept.positionTypes.map((pt) => (pt.id === positionTypeId ? { ...pt, checked } : pt));

          // 根据岗位类型的选中情况更新部门的选中状态
          const checkedCount = updatedPositionTypes.filter((pt) => pt.checked).length;
          const totalCount = updatedPositionTypes.length;
          const departmentChecked = checkedCount === totalCount;

          return {
            ...dept,
            checked: departmentChecked,
            positionTypes: updatedPositionTypes,
          };
        }
        return dept;
      }),
    );
  };

  const handleOk = () => {
    const values = {
      selectedForms: currentSelectedForms,
      authorizationType,
      functionPermissions,
      doNotDisturb,
      selectedPositions,
      departments,
    };

    if (currentSelectedForms.length === 0) {
      message.warning('请至少选择一个表单');
      return;
    }

    if (authorizationType === 'specific' && selectedPositions.length === 0) {
      message.warning('请至少选择一个岗位');
      return;
    }

    onOk(values);
  };

  // 提示语
  const getAuthorizationHint = () => {
    switch (authorizationType) {
      case 'all':
        return '提示：授权操作岗位管理范围内所有岗位，例如：公司法人管理岗操作则是公司所有岗位部门管理岗操作则是部门所有岗位';
      case 'type':
        return '提示：授权操作岗位管理范围内满足岗位类型的所有岗位，例如：公司法人管理岗操作则是公司所有满足岗位类型的岗位，部门管理岗操作则是部门满足岗位类型的所有岗位';
      case 'specific':
        return '提示：授权操作岗位管理范围内指定岗位';
      default:
        return '';
    }
  };

  return (
    <Modal
      open
      title="授权管理"
      onCancel={onCancel}
      onOk={handleOk}
      width={600}
      className={styles.authorizationModal}
      footer={
        <div className={styles.footer}>
          <div className={styles.doNotDisturbSection}>
            <span>开启免打扰</span>
            <Tooltip title="免打扰功能说明">
              <QuestionCircleOutlined className={styles.questionIcon} />
            </Tooltip>
            <span>：</span>
            <Checkbox checked={doNotDisturb} onChange={(e) => setDoNotDisturb(e.target.checked)}>
              授权人
            </Checkbox>
          </div>
          <div className={styles.btnBox}>
            <Button key="cancel" onClick={onCancel}>
              取消
            </Button>
            <Button key="confirm" type="primary" onClick={handleOk}>
              确定
            </Button>
          </div>
        </div>
      }
    >
      <div className={styles.content}>
        {/* 已选表单 */}
        <div className={styles.section} style={{ marginBottom: 16 }}>
          <div className={styles.sectionTitle}>已选表单：</div>
          <Select
            mode="multiple"
            value={currentSelectedForms.map((form) => form.id)}
            placeholder="请选择表单"
            style={{ flex: 1 }}
            options={selectedForms.map((form) => ({
              label: form.name,
              value: form.id,
            }))}
            onChange={(values) => {
              const forms = selectedForms.filter((form) => values.includes(form.id));
              setCurrentSelectedForms(forms);
            }}
            className={styles.formSelect}
            showSearch
            filterOption={(input, option) =>
              String(option?.label ?? '')
                .toLowerCase()
                .includes(input.toLowerCase())
            }
          />
        </div>

        {/* 表单授权 */}
        <div className={styles.section}>
          <div className={styles.sectionTitle}>表单授权：</div>
          <Radio.Group value={authorizationType} onChange={(e) => handleAuthorizationTypeChange(e.target.value)} className={styles.radioGroup}>
            <Radio value="all">所有岗位</Radio>
            <Radio value="type">岗位类型</Radio>
            <Radio value="specific">指定岗位</Radio>
          </Radio.Group>
        </div>

        <div className={styles.hintText}>{getAuthorizationHint()}</div>

        {/* 岗位类型选择 */}
        {authorizationType === 'type' && (
          <div className={styles.positionTypeSection}>
            {departments.map((dept) => {
              const checkState = getDepartmentCheckState(dept);
              return (
                <div key={dept.id} className={styles.departmentRow}>
                  <Checkbox
                    checked={checkState.checked}
                    indeterminate={checkState.indeterminate}
                    onChange={(e) => handleDepartmentChange(dept.id, e.target.checked)}
                    className={styles.departmentCheckbox}
                  >
                    {dept.name}
                  </Checkbox>
                  <div className={styles.positionTypes}>
                    {dept.positionTypes.map((pt, index) => (
                      <div key={index}>
                        <Checkbox key={pt.id} checked={pt.checked} onChange={(e) => handlePositionTypeChange(dept.id, pt.id, e.target.checked)}>
                          {pt.name}
                        </Checkbox>
                        {!!pt.tooltipMsg && (
                          <Tooltip title={pt.tooltipMsg}>
                            <QuestionCircleOutlined className={styles.questionIcon} />
                          </Tooltip>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              );
            })}
          </div>
        )}

        {/* 指定岗位选择 */}
        {authorizationType === 'specific' && (
          <div className={styles.specificPositionSection}>
            <PositionSelector selectedPositions={selectedPositions} setSelectedPositions={setSelectedPositions} />
          </div>
        )}

        {/* 功能权限 */}
        <div className={styles.sectionTitle} style={{ marginBottom: 8 }}>
          功能权限
        </div>
        <div className={styles.section}>
          <div>授权后拥有权限：</div>
          <div>
            <Checkbox disabled checked={functionPermissions.formUsage}>
              表单使用
            </Checkbox>
            <Checkbox disabled checked={functionPermissions.formAuthorization}>
              表单授权 (管理岗)
            </Checkbox>
            <Checkbox
              checked={functionPermissions.topLevelAuthorization}
              onChange={(e) => handleFunctionPermissionChange('topLevelAuthorization', e.target.checked)}
            >
              顶级授权 (绕过读写权限)
            </Checkbox>
          </div>
        </div>
      </div>
    </Modal>
  );
};

export default AuthorizationManagement;
