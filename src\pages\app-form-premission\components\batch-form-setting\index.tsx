import IconFormApprove from '@/assets/app-form-manage/icon-form-approve.png';
import IconFormExcute from '@/assets/app-form-manage/icon-form-execution.png';
import { MetaAppStatus } from '@/const/metadata';
import { formatDateTime } from '@/utils';
import { BarsOutlined } from '@ant-design/icons';
import { Button, Checkbox, Collapse, Divider, Empty, Input, message, Modal, Tabs, TextEllipsisTooltip } from '@gwy/components-web';
import { useState } from 'react';
import AuthorizationManagement from '../authorization-management';
import { executeTagType, tagType } from '../manage-list/card';
import styles from './index.less';

interface Iprops {
  onOk?: () => void;
  onCancel?: () => void;
  appList: any[];
  batchIcon?: React.ReactNode;
  batchStyle?: React.CSSProperties;
}

const BatchFormSetting = (props: Iprops) => {
  const { onOk, onCancel, appList, batchIcon, batchStyle } = props;
  const [activeKey, setActiveKey] = useState('4');
  const [searchInput, setSearchInput] = useState('');
  const [checkedApp, setCheckedApp] = useState<Array<{ id: string; name: string }>>([]);
  const [batchOpen, setBatchOpen] = useState(false);
  // 授权管理弹窗
  const [authorizeModal, setAuthorizeModal] = useState<{
    visible: boolean;
    selectedForms: any[];
  }>({
    visible: false,
    selectedForms: [],
  });
  console.log('checkedApp', checkedApp);
  const renderCardApp = (item: any) => {
    const { formVersionId, appType, name, postName, userName, userId, createTime, status, enable } = item;
    const isExcuteForm = appType === 'SUBMIT';
    return (
      <div
        className={styles.cardWrapper}
        key={formVersionId}
        onDragStart={(e) => {
          e.dataTransfer.setData('text', item?.id);
        }}
        onDragOver={(e) => {
          e.preventDefault();
        }}
      >
        <header className={styles.headerBox}>
          <img src={isExcuteForm ? IconFormExcute : IconFormApprove} width={31} height={36} />
          <div style={{ flex: 1, overflow: 'hidden' }}>
            <article className={styles.headerInfo}>
              <div className={styles.titleBox}>
                <span className={styles.title}>
                  <TextEllipsisTooltip text={name} />
                </span>
                <div className={styles.tagBox}>
                  <div
                    className={styles.tag}
                    style={{
                      color: tagType[String(enable)]?.color,
                      backgroundColor: tagType[String(enable)]?.backgroundColor,
                      border: tagType[String(enable)]?.border,
                    }}
                  >
                    {tagType[String(enable)]?.label}
                  </div>
                  {status === MetaAppStatus.Stash && (
                    <div
                      className={styles.tag}
                      style={{
                        color: executeTagType?.[status]?.color,
                        backgroundColor: executeTagType?.[status]?.backgroundColor,
                        border: `1px solid ${executeTagType?.[status]?.color}`,
                        minWidth: '44px',
                      }}
                    >
                      {executeTagType?.[status]?.label}
                    </div>
                  )}
                </div>
              </div>
            </article>
            <div className={styles.timeLabel}>
              <span>{postName}</span>
              {userId && <span>/{userName}</span>}
            </div>
          </div>
        </header>
        <Divider style={{ margin: 0 }} />
        <aside className={styles.contentBox}>
          {/* <div className={styles.infoBox}></div> */}
          <div className={styles.footerBox}>
            <div className={styles.timeBox}>
              <span>创建时间：</span>
              <span>{formatDateTime(createTime)}</span>
            </div>
            <Checkbox value={formVersionId} key={formVersionId} />
          </div>
        </aside>
      </div>
    );
  };

  const renderList = (type?: string) => {
    if (!appList.length)
      return (
        <div className={styles.emptyContainer}>
          <Empty description="暂无数据" />
        </div>
      );

    // 按类型分组表单
    const formGroups = {
      management: appList.find((item) => item.appType !== 'SUBMIT'),
      execution: appList.find((item) => item.appType === 'SUBMIT'),
    };

    // 过滤搜索结果
    const getFilteredGroups = () => {
      switch (type) {
        case 'authorize':
          return {
            management: formGroups.management?.list?.filter((item) => item?.name?.includes(searchInput) && item?.enable === true),
            execution: formGroups.execution?.list?.filter((item) => item?.name?.includes(searchInput) && item?.enable === true),
          };
        default:
          return {
            management: formGroups.management?.list?.filter((item) => item?.name?.includes(searchInput)),
            execution: formGroups.execution?.list?.filter((item) => item?.name?.includes(searchInput)),
          };
      }
    };
    const filteredGroups = getFilteredGroups();

    const renderSection = (forms: any[], title: string, sectionKey: string) => {
      if (!forms.length) return null;

      const checkedForms = checkedApp.filter((item) => forms.some((form) => form.formVersionId === item.id));
      const isAllChecked = forms.length > 0 && checkedForms.length === forms.length;
      const isIndeterminate = checkedForms.length > 0 && checkedForms.length < forms.length;

      const handleSelectAll = (checked: boolean) => {
        const formData = forms.map((form) => ({ id: form.formVersionId, name: form.name }));
        setCheckedApp((prev) => {
          if (checked) {
            const newItems = [...prev];
            formData.forEach((item) => {
              if (!newItems.some((existing) => existing.id === item.id)) {
                newItems.push(item);
              }
            });
            return newItems;
          } else {
            return prev.filter((item) => !formData.some((form) => form.id === item.id));
          }
        });
      };

      return (
        <Collapse defaultActiveKey={[sectionKey]} style={{ marginBottom: 10 }} key={sectionKey} collapsible="header" ghost>
          <Collapse.Panel
            header={title}
            extra={
              <Checkbox
                className={styles.sectionSelectAll}
                indeterminate={isIndeterminate}
                checked={isAllChecked}
                onChange={(e) => handleSelectAll(e.target.checked)}
              >
                全选
              </Checkbox>
            }
            key={sectionKey}
          >
            <div className={styles.formGrid}>
              <Checkbox.Group
                value={checkedApp.map((item) => item.id)}
                onChange={(values) => {
                  const selectedForms = forms.filter((form) => values.includes(form.formVersionId));
                  const formData = selectedForms.map((form) => ({ id: form.formVersionId, name: form.name }));
                  setCheckedApp(formData);
                }}
                className={styles.appListCheck}
              >
                {forms.map(renderCardApp)}
              </Checkbox.Group>
            </div>
          </Collapse.Panel>
        </Collapse>
      );
    };

    const hasResults = filteredGroups.management || filteredGroups.execution;

    return (
      <div className={styles.tabChildren}>
        <div className={styles.headerWrapper}>
          <div className={styles.searchWrapper}>
            <Input.Search className={styles.search} value={searchInput} onChange={(e) => setSearchInput(e.target.value)} placeholder="搜索表单名称" />
          </div>
        </div>
        <div className={styles.bodyWrapper}>
          {renderSection(filteredGroups.management, '管理表单', 'management')}
          {renderSection(filteredGroups.execution, '执行表单', 'execution')}
          {!hasResults && (
            <div className={styles.emptyContainer}>
              <Empty description="暂无数据" />
            </div>
          )}
        </div>
      </div>
    );
  };

  const items = [
    // {
    //   label: '批量禁用',
    //   key: '1',
    //   children: renderList(),
    // },
    // {
    //   label: '批量启用',
    //   key: '2',
    //   children: renderList(),
    // },
    // {
    //   label: '批量删除',
    //   key: '3',
    //   children: renderList(),
    // },
    {
      label: '批量授权',
      key: '4',
      children: renderList('authorize'),
    },
    {
      label: '批量核准配置',
      key: '5',
      children: renderList(),
    },
  ];

  return (
    <>
      <Button
        type="link"
        icon={
          batchIcon ? (
            batchIcon
          ) : (
            <BarsOutlined
              style={{
                color: '#4b87fb',
                marginRight: 0,
                ...(batchStyle || {}),
              }}
            />
          )
        }
        style={{
          marginRight: 10,
          color: '#4b87fb',
          ...(batchStyle || {}),
        }}
        onClick={(e) => {
          e.stopPropagation();
          setBatchOpen(true);
          setCheckedApp([]);
        }}
      >
        批量操作
      </Button>

      {batchOpen && (
        <Modal
          title="批量设置"
          wrapClassName={styles.modal}
          width={800}
          onCancel={() => {
            onCancel?.();
            setCheckedApp([]);
            setBatchOpen(false);
          }}
          onOk={() => {
            switch (activeKey) {
              case '4':
                if (!checkedApp.length) {
                  return message.info('至少选择一个表单进行授权');
                }
                setAuthorizeModal({ visible: true, selectedForms: checkedApp });
              default:
                if (!checkedApp.length) {
                  return message.info('请选择表单');
                }
            }
            onOk?.();
            setCheckedApp([]);
            setBatchOpen(false);
          }}
          open={batchOpen}
        >
          <div className={styles.batchSetting}>
            <Tabs activeKey={activeKey} onChange={(key) => setActiveKey(key)} type="card" size="small" items={items}></Tabs>
          </div>
        </Modal>
      )}

      {authorizeModal.visible && (
        <AuthorizationManagement
          selectedForms={authorizeModal.selectedForms}
          onOk={() => {}}
          onCancel={() => {
            setAuthorizeModal({ visible: false, selectedForms: [] });
          }}
        />
      )}
    </>
  );
};

export default BatchFormSetting;
