const TMAP_KEY = 'QI7BZ-ANQCD-6QU4P-HMX2T-KANFK-YDBDJ';
const BAIDU_MAP = 'https://api.map.baidu.com/api?v=2.0&ak=4ZQKYAaGU1N3tCPRUAu8wKSE7aBkGOgf&callback=onBMapCallBack';

export const MapType = {
  // 0：百度 1: 腾讯
  BMap: 0,
  TMap: 1,
};

let mapType = null;
function setMapType(remoteMapType) {
  mapType = remoteMapType;
}
export { mapType };

// 动态加载百度地图
function loadScript(callback) {
  window.onBMapCallBack = function () {
    loadMap(callback);
  };
  // 防止多次调用重复加载
  if (document.querySelector('script[data-name]')) return;
  const script = document.createElement('script');
  script.setAttribute('src', BAIDU_MAP);
  script.setAttribute('data-name', 'BAIDU_MAP');
  script.type = 'text/javascript';
  document.body.appendChild(script);
}

export function loadMap(callback) {
  // if (typeof BMap === "undefined" || !BMap || !BMap?.Map) {
  //   loadScript(callback)
  //   return
  // }
  if (getMapLoaded()) {
    callback && callback();
    return;
  }
  // _axios.get('/ideal-new-user/api/user/map/resource').then((res) => {
  //   setMapType(res.data);
  //   const fn = () => {
  //     if (getMapLoaded()) {
  //       callback && callback();
  //     } else {
  //       setTimeout(fn, 100);
  //     }
  //   };
  //   fn();
  // });
  setMapType(MapType.BMap);
  const fn = () => {
    if (getMapLoaded()) {
      callback && callback();
    } else {
      setTimeout(fn, 100);
    }
  };
  fn();
}

function getMapLoaded() {
  return (mapType === MapType.BMap && BMap) || (mapType === MapType.TMap && qq.maps);
}

export function createMap(container, options) {
  if (mapType === MapType.BMap) {
    const { enableMapClick } = options || {};
    return new BMap.Map(container, {
      enableMapClick,
    });
  } else if (mapType === MapType.TMap) {
    const { mapTypeControl = false, panControl = false, zoomControl = false } = options || {};
    return new qq.maps.Map(container, {
      mapTypeControl,
      panControl,
      zoomControl,
    });
  }
}

export function initPoint(longtitude, latitude) {
  if (mapType === MapType.BMap) {
    return new BMap.Point(longtitude, latitude);
  } else if (mapType === MapType.TMap) {
    return new qq.maps.LatLng(latitude, longtitude);
  }
}

export function initMapIcon(icon, width, height) {
  if (mapType === MapType.BMap) {
    return new BMap.Icon(icon, new BMap.Size(width, height));
  } else if (mapType === MapType.TMap) {
    return new qq.maps.MarkerImage(icon, new qq.maps.Size(width, height));
  }
}

export function initMapMarker(point, options) {
  if (mapType === MapType.BMap) {
    return new BMap.Marker(point, options);
  } else if (mapType === MapType.TMap) {
    const { icon } = options || {};
    const markerOptions = icon ? { position: point, icon } : { position: point };
    return new qq.maps.Marker(markerOptions);
  }
}

export function markerAddEventListener(marker, callback) {
  if (mapType === MapType.BMap) {
    marker.addEventListener('click', callback);
  } else if (mapType === MapType.TMap) {
    qq.maps.event.addListener(marker, 'click', callback);
  }
}

export function initMapCircle(point, radius, opts) {
  if (mapType === MapType.BMap) {
    return new BMap.Circle(point, radius, opts);
  } else if (mapType === MapType.TMap) {
    let { strokeColor, fillColor, strokeWeight, fillOpacity, strokeOpacity } = opts;
    const { r, g, b } = hexToRgb(strokeColor);
    strokeColor = new qq.maps.Color(r, g, b, strokeOpacity);
    const { r2, g2, b2 } = hexToRgb(fillColor);
    fillColor = new qq.maps.Color(r2, g2, b2, fillOpacity);
    return new qq.maps.Circle({
      center: point,
      radius: Number(radius) || 500,
      strokeColor,
      fillColor,
      strokeWeight,
    });
  }
}

function hexToRgb(hex) {
  var result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
  return result
    ? {
        r: parseInt(result[1], 16),
        g: parseInt(result[2], 16),
        b: parseInt(result[3], 16),
      }
    : null;
}

export function initPolyline(points, opts) {
  if (mapType === MapType.BMap) {
    return new BMap.Polyline(points, opts);
  } else if (mapType === MapType.TMap) {
    let { enableEditing, enableClicking, strokeWeight, strokeOpacity, strokeColor } = opts || {};
    const { r, g, b } = hexToRgb(strokeColor);
    strokeColor = new qq.maps.Color(r, g, b, strokeOpacity);
    return new qq.maps.Polyline({
      path: points,
      strokeColor,
      strokeWeight,
      editable: enableEditing,
      clickable: enableClicking,
    });
  }
}

export function getLocalCityPosition() {
  return new Promise((resolve) => {
    loadMap(() => {
      if (mapType === MapType.BMap) {
        let myCity = new BMap.LocalCity();
        myCity.get((result) => {
          let lon = result.center.lng;
          let lat = result.center.lat;
          const name = result.name;
          resolve({ lon, lat, name });
        });
      } else if (mapType === MapType.TMap) {
        //设置城市信息查询服务
        const citylocation = new qq.maps.CityService();
        //请求成功回调函数
        citylocation.setComplete(function (result) {
          const latLng = result.detail.latLng;
          const name = result.detail.name;
          resolve({ lon: latLng.getLng(), lat: latLng.getLat(), name });
        });
        //请求失败回调函数
        citylocation.setError(function () {
          console.log('出错了，请输入正确的经纬度！！！');
        });
        citylocation.searchLocalCity();

        // 有跨域问题，封装的jsonp调用，在此项目有问题，在单独的index.html中测试没问题，估计和打包有关
        // _axios
        //   .jsonp("https://apis.map.qq.com/ws/location/v1/ip", { key: TMAP_KEY, output: "jsonp" })
        //   .then(function(backData) {
        //     console.log(backData)
        //     if (!backData.results) {
        //       return
        //     }
        //   })
      }
    });
  });
}

export function centerAndZoom(map, point, zoom) {
  if (mapType === MapType.BMap) {
    map.centerAndZoom(point, zoom);
  } else if (mapType === MapType.TMap) {
    map.setCenter(point);
    map.setZoom(zoom);
  }
}

export async function getLocationAndCenter(map, callback) {
  const { lon, lat } = await getLocalCityPosition();
  centerAndZoom(map, initPoint(lon, lat), 18);
  if (callback && typeof callback === 'function') {
    callback();
  }
}

export function getLocation(longtitude, latitude) {
  if (mapType === MapType.BMap) {
    return new Promise((resolve) => {
      let point = initPoint(longtitude, latitude);
      const geoc = new BMap.Geocoder();
      geoc.getLocation(point, (res) => {
        resolve(res.address);
      });
    });
  } else if (mapType === MapType.TMap) {
    return new Promise((resolve) => {
      //地址和经纬度之间进行转换服务
      const geocoder = new qq.maps.Geocoder();
      //设置服务请求成功的回调函数
      geocoder.setComplete(function (result) {
        resolve(result.detail.address);
      });
      //若服务请求失败，则运行以下函数
      geocoder.setError(function () {
        console.log('出错了，请输入正确的经纬度！！！');
      });

      var latLng = new qq.maps.LatLng(latitude, longtitude);
      //对指定经纬度进行解析
      geocoder.getAddress(latLng);
    });
  }
}

export function openInfoWindow(map, content, opts, point, infoWindowCloseCallbak) {
  if (mapType === MapType.BMap) {
    let infoWindow = new BMap.InfoWindow(content, opts);
    if (infoWindowCloseCallbak) {
      infoWindow.addEventListener('close', infoWindowCloseCallbak);
    }
    map?.openInfoWindow(infoWindow, point); //开启信息窗口

    return infoWindow;
  } else if (mapType === MapType.TMap) {
    var infoWin = new qq.maps.InfoWindow({
      map: map,
    });
    infoWin.open();
    //tips  自定义内容
    infoWin.setContent(content);
    infoWin.setPosition(point);

    return infoWin;
  }
}

export function closeInfoWindow(map, infoWin, infoWindowCloseCallbak) {
  if (mapType === MapType.BMap) {
    map.closeInfoWindow();
  } else if (mapType === MapType.TMap) {
    infoWin && infoWin.close();
    infoWindowCloseCallbak && infoWindowCloseCallbak();
  }
}

export function enableScrollWheelZoom(map) {
  if (mapType === MapType.BMap) {
    map.enableScrollWheelZoom(); //启用滚轮放大缩小
  } else if (mapType === Map.TMap) {
    map.setOptions({
      scrollwheel: true,
    });
  }
}

export function mapDisableDoubleClickZoom(map) {
  if (mapType === MapType.BMap) {
    map.disableDoubleClickZoom();
  } else if (mapType === MapType.TMap) {
    map.setOptions({
      disableDoubleClickZoom: true,
    });
  }
}

export function panTo(map, center, opts) {
  if (mapType === MapType.BMap) {
    map.panTo(center, opts);
  } else if (mapType === MapType.TMap) {
    map.panTo(center, opts);
  }
}

export function mapSetZoom(map, zoom) {
  if (mapType === MapType.BMap) {
    map.setZoom(zoom);
  } else if (mapType === MapType.TMap) {
    map.setZoom(zoom);
  }
}

/**
 *
 * @param {*} map
 * @param {*} points
 * @param {
 *  margins: [0, 0, 0, 280], // 设定的地理范围与可视窗口之间的距离
 * } options
 */
export function mapSetViewport(map, points, options) {
  const { margins } = options || {};
  if (mapType === MapType.BMap) {
    map.setViewport(points, {
      margins: Array.isArray(margins) ? margins : null,
    });
  } else if (mapType === MapType.TMap) {
    const [top = 0, right = 0, bottom = 0, left = 0] = margins || [];
    const padding = Array.isArray(margins) ? { top, right, bottom, left } : 0;
    map.fitBounds(getLatLngBounds(points), { padding });
  }
}

export function mapGetViewport(map) {
  if (mapType === MapType.BMap) {
    return map.getViewport();
  } else if (mapType === MapType.TMap) {
    const center = map.getCenter();
    const lat = center.getLat();
    const lng = center.getLng();
    return {
      center: {
        lat,
        lng,
      },
    };
  }
}

function getLatLngBounds(latLngList) {
  const center = initPoint(120.071866, 30.317663);
  latLngList = latLngList || [center];
  const bounds = new qq.maps.LatLngBounds();
  latLngList.forEach((latLng) => {
    bounds.extend(latLng);
  });
  return bounds;
}

export function pointToOverlayPixel(map, point, overlay) {
  if (mapType === MapType.BMap) {
    return map.pointToOverlayPixel(point);
  } else if (mapType === MapType.TMap) {
    //返回覆盖物容器的相对像素坐标
    return overlay.getProjection().fromLatLngToDivPixel(point);
  }
}

export function initOverLay() {
  if (mapType === MapType.BMap) {
    return new BMap.Overlay();
  } else if (mapType === MapType.TMap) {
    return new qq.maps.Overlay();
  }
}

export function getCustomeOverlayInitializeKey() {
  if (mapType === MapType.BMap) {
    return 'initialize';
  } else if (mapType === MapType.TMap) {
    return 'construct';
  }
}

export function addOverlay(map, overlay) {
  if (mapType === MapType.BMap) {
    map.addOverlay(overlay);
  } else if (mapType === MapType.TMap) {
    overlay && overlay.setMap(map);
    // 记录_overlays
    if (!map._overlays) {
      map._overlays = [];
    }
    map._overlays.push(overlay);
  }
}

export function removeOverlay(map, overlay) {
  if (mapType === MapType.BMap) {
    map.removeOverlay(overlay);
  } else if (mapType === MapType.TMap) {
    overlay && overlay.setMap(null);
    // 记录_overlays
    if (map._overlays) {
      map._overlays = map._overlays.filter((item) => item !== overlay);
    }
  }
}

export function clearOverlays(map) {
  if (mapType === MapType.BMap) {
    map.clearOverlays();
  } else if (mapType === MapType.TMap) {
    if (map._overlays) {
      map._overlays.forEach((overlay) => overlay.setMap(null));
    }
    // 记录_overlays
    map._overlays = [];
  }
}

export function getOverlays(map) {
  if (mapType === MapType.BMap) {
    return map.getOverlays();
  } else if (mapType === MapType.TMap) {
    return map._overlays || [];
  }
}

export function paneAddChild(map, overlay, child) {
  if (mapType === MapType.BMap) {
    map.getPanes().labelPane.appendChild(child);
  } else if (mapType === MapType.TMap) {
    overlay.getPanes().overlayMouseTarget.appendChild(child);
  }
}

export function addZoomChangeListener(map, callback) {
  if (mapType === MapType.BMap) {
    function cb(e) {
      const { target } = e;
      const { Na } = target || {};
      callback(Na, e);
    }
    map.addEventListener('zoomend', cb);
    return cb;
  } else if (mapType === MapType.TMap) {
    function cb(e) {
      const { target } = e;
      const { zoom } = target || {};
      callback(zoom, e);
    }
    return qq.maps.event.addListener(map, 'zoom_changed', cb);
  }
}

export function removeZoomChangeListener(map, listener) {
  if (_.isEmpty(map)) return;
  if (mapType === MapType.BMap) {
    map.removeEventListener('zoom_changed', listener);
  } else if (mapType === MapType.TMap) {
    return qq.maps.event.removeListener(listener);
  }
}

export function addEventListener(obj, event, listener) {
  if (mapType === MapType.BMap) {
    obj.addEventListener(event, listener);
    return listener;
  } else if (mapType === MapType.TMap) {
    return qq.maps.event.addListener(obj, event, listener);
  }
}

export function removeEventListener(obj, event, listener) {
  if (_.isEmpty(obj)) return;
  if (mapType === MapType.BMap) {
    obj.removeEventListener(event, listener);
  } else if (mapType === MapType.TMap) {
    qq.maps.event.removeListener(listener);
  }
}

export function addMapClickListener(map, listener) {
  return addEventListener(map, 'click', listener);
}

export function removeMapClickListener(map, listener) {
  removeEventListener(map, 'click', listener);
}

export function addInfoWindowCloseClickListener(infoWindow, listener) {
  if (mapType === MapType.BMap) {
    addEventListener(infoWindow, 'clickclose', listener);
  } else if (mapType === MapType.TMap) {
    addEventListener(infoWindow, 'closeclick', listener);
  }
}
