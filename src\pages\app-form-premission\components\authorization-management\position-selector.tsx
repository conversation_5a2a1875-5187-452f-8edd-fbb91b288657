/* 指定岗位选择组件 */
import { CloseCircleFilled } from '@ant-design/icons';
import { Avatar, Checkbox, ConfigProvider, Input, TextEllipsisTooltip, Tree } from '@gwy/components-web';
import { useEffect, useMemo, useState } from 'react';
import { positionSelectionData } from './const';
import styles from './index.less';

interface IProps {
  selectedPositions: any;
  setSelectedPositions?: (positions: any[]) => void;
}

const PositionSelector = ({ selectedPositions, setSelectedPositions }: IProps) => {
  const [searchText, setSearchText] = useState('');
  const [filteredData, setFilteredData] = useState<any[]>(positionSelectionData);

  // 获取部门下所有岗位（包括子部门）
  const getDepartmentPositions = (department: any): any[] => {
    let positions: any[] = [];

    // 添加当前部门的岗位
    if (department.posts && department.posts.length > 0) {
      positions.push(...department.posts);
    }

    // 递归添加子部门的岗位
    if (department.children && department.children.length > 0) {
      department.children.forEach((child: any) => {
        positions.push(...getDepartmentPositions(child));
      });
    }

    return positions;
  };

  // 检查部门是否被全选
  const isDepartmentFullySelected = (department: any): boolean => {
    const allPositions = getDepartmentPositions(department);
    return allPositions.length > 0 && allPositions.every((post) => selectedPositions.some((p) => p.postId === post.postId));
  };

  // 检查部门是否被部分选中
  const isDepartmentPartiallySelected = (department: any): boolean => {
    const allPositions = getDepartmentPositions(department);
    if (allPositions.length === 0) return false;

    const selectedCount = allPositions.filter((post) => selectedPositions.some((p) => p.postId === post.postId)).length;

    return selectedCount > 0 && selectedCount < allPositions.length;
  };

  // 将数据转换为 Tree 组件需要的格式
  const treeData = useMemo(() => {
    const convertToTreeData = (departments) => {
      return departments.map((dept) => {
        const children = [];

        // 添加子部门
        if (dept.children.length > 0) {
          children.push(...convertToTreeData(dept.children));
        }

        // 添加岗位
        if (dept.posts.length > 0) {
          dept.posts.forEach((post) => {
            children.push({
              key: `position-${post.postId}`,
              title: (
                <div className={styles.positionItem}>
                  <Checkbox
                    checked={selectedPositions.some((p) => p.postId === post.postId)}
                    onChange={(e) => handlePositionSelect(post, e.target.checked)}
                  >
                    <div className={styles.positionInfo}>
                      <Avatar size={24} src={post.avatar} />
                      <div className={styles.positionDetails}>
                        <div className={styles.userName}>
                          <TextEllipsisTooltip text={post.userName} />
                        </div>
                        <div className={styles.postName}>
                          <TextEllipsisTooltip text={post.postName} />
                        </div>
                      </div>
                    </div>
                  </Checkbox>
                </div>
              ),
              isLeaf: true,
              data: post,
              type: 'position',
            });
          });
        }

        return {
          key: `department-${dept.departmentId}`,
          title: (
            <div className={styles.departmentHeader}>
              <Checkbox
                checked={isDepartmentFullySelected(dept)}
                indeterminate={isDepartmentPartiallySelected(dept)}
                onChange={(e) => handleDepartmentSelect(dept, e.target.checked)}
              >
                <span className={styles.departmentName}>
                  <TextEllipsisTooltip text={dept.departmentName} />
                </span>
              </Checkbox>
            </div>
          ),
          children: children.length > 0 ? children : undefined,
          data: dept,
          type: 'department',
        };
      });
    };

    return convertToTreeData(filteredData);
  }, [filteredData, selectedPositions]);

  // 搜索过滤
  useEffect(() => {
    if (!searchText.trim()) {
      setFilteredData(positionSelectionData);
      return;
    }

    const filterData = (departments) => {
      return departments
        .map((dept) => {
          // 检查当前部门名称是否匹配搜索词
          const deptNameMatches = dept.departmentName.toLowerCase().includes(searchText.toLowerCase());

          // 递归过滤子部门
          const filteredChildren = filterData(dept.children);

          // 过滤当前部门的岗位
          const filteredPosts = dept.posts.filter(
            (post) =>
              post.userName.toLowerCase().includes(searchText.toLowerCase()) ||
              post.postName.toLowerCase().includes(searchText.toLowerCase()) ||
              dept.departmentName.toLowerCase().includes(searchText.toLowerCase()),
          );

          // 如果当前部门名称匹配，或者有匹配的子部门，或者有匹配的岗位，则保留该部门
          if (deptNameMatches || filteredChildren.length > 0 || filteredPosts.length > 0) {
            return {
              ...dept,
              children: deptNameMatches ? dept.children : filteredChildren, // 如果部门名称匹配，保留所有子部门
              posts: deptNameMatches ? dept.posts : filteredPosts, // 如果部门名称匹配，保留所有岗位
            };
          }
          return null;
        })
        .filter(Boolean);
    };

    setFilteredData(filterData(positionSelectionData));
  }, [searchText]);

  // 选择岗位
  const handlePositionSelect = (position: any, checked: boolean) => {
    if (checked) {
      setSelectedPositions([...selectedPositions, position]);
    } else {
      setSelectedPositions(selectedPositions.filter((p) => p.postId !== position.postId));
    }
  };

  // 选择部门
  const handleDepartmentSelect = (department: any, checked: boolean) => {
    const allPositions = getDepartmentPositions(department);

    if (checked) {
      // 选中部门下所有岗位
      const newPositions = allPositions.filter((post) => !selectedPositions.some((p) => p.postId === post.postId));
      setSelectedPositions([...selectedPositions, ...newPositions]);
    } else {
      // 取消选中部门下所有岗位
      setSelectedPositions(selectedPositions.filter((p) => !allPositions.some((post) => post.postId === p.postId)));
    }
  };

  // 移除已选岗位
  const removePosition = (positionId: string) => {
    setSelectedPositions(selectedPositions.filter((p) => p.postId !== positionId));
  };

  return (
    <div className={styles.positionSelector}>
      {/* 左侧选择面板 */}
      <div className={styles.selectionPanel}>
        <Input.Search placeholder="搜索部门、岗位、姓名" value={searchText} onChange={(e) => setSearchText(e.target.value)} />

        <div className={styles.departmentList}>
          {treeData.length && (
            <ConfigProvider
              theme={{
                components: {
                  Tree: {
                    nodeSelectedBg: '#FFFFFF',
                    nodeSelectedColor: '#4D7BF6',
                    nodeHoverColor: '#4D7BF6',
                    nodeHoverBg: 'none',
                    colorText: '#1D2129',
                    indentSize: 12,
                  },
                },
              }}
            >
              <Tree blockNode defaultExpandAll treeData={treeData} />
            </ConfigProvider>
          )}
        </div>
      </div>

      {/* 右侧已选岗位面板 */}
      <div className={styles.selectedPanel}>
        <div className={styles.selectedTitle}>已选岗位</div>
        <div className={styles.selectedList}>
          {selectedPositions.length === 0 ? (
            <div className={styles.emptyState}>暂无已选岗位</div>
          ) : (
            selectedPositions.map((position) => (
              <div key={position.postId} className={styles.selectedPosition}>
                <div className={styles.positionInfo}>
                  <Avatar size={32} src={position.avatar} />
                  <div className={styles.positionDetails}>
                    <div className={styles.userName}>
                      <TextEllipsisTooltip text={position.userName} />
                    </div>
                    <div className={styles.postName}>
                      <TextEllipsisTooltip text={position.postName} />
                    </div>
                  </div>
                </div>
                <CloseCircleFilled className={styles.removeButton} onClick={() => removePosition(position.postId)} />
              </div>
            ))
          )}
        </div>
      </div>
    </div>
  );
};

export default PositionSelector;
