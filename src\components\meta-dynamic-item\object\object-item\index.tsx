import { datasourceDataAPI } from '@/services';
import { FormField } from '@/types/form-field';
import { DataUnitTagVO } from '@/types/tag';
import { Select } from '@gwy/components-web';
import { useEffect, useMemo, useState } from 'react';
import { UseIn } from '../../const';
import { ValueFormat } from '../const';

type ValueItem = {
  data?: string;
  _id?: string;
  value?: string;
  original_data_id?: string;
};

type Props = {
  tag?: DataUnitTagVO;
  field?: FormField;
  value?: any;
  onChange?: (value) => void;
  useIn?: UseIn;
  valueFormat?: ValueFormat;
  disabled?: boolean;
};

const ObjectItem = ({ tag, field, value, onChange, useIn = UseIn.App, valueFormat = ValueFormat.ObjectArray, disabled }: Props) => {
  let multipleChoice = field?.config?.multipleChoice;
  if (useIn === UseIn.Filter) {
    multipleChoice = true;
  }

  const [list, setList] = useState<ValueItem[]>([]);
  const options = useMemo(() => {
    return list?.map((item) => ({ label: item.value, value: item._id }));
  }, [list]);

  const fetchOptions = async () => {
    const data = await datasourceDataAPI.queryIdentifierTagData(useIn === UseIn.Filter ? tag.tagMetaDataConfig : field.config);
    setList(data);
  };

  useEffect(() => {
    fetchOptions();
  }, []);

  const _value = useMemo(() => {
    if (multipleChoice) {
      if (valueFormat === ValueFormat.ObjectArray) {
        return (value as ValueItem[])?.map((item) => item._id);
      }
      if (valueFormat === ValueFormat.ValueArray) {
        return value;
      }
      // 多选情况下，valueFormat 为 Object、Value 时，不合理，所以不处理
      return value;
    }

    if (valueFormat === ValueFormat.ObjectArray) {
      return (value as ValueItem[])?.[0]?._id;
    }
    if (valueFormat === ValueFormat.ValueArray) {
      return value?.[0];
    }
    if (valueFormat === ValueFormat.Object) {
      return (value as ValueItem)?._id;
    }

    return value;
  }, [multipleChoice, value, valueFormat]);

  return (
    <Select
      placeholder={field?.placeholder || '请选择'}
      options={options}
      mode={multipleChoice ? 'multiple' : undefined}
      value={_value}
      disabled={disabled}
      onChange={(value) => {
        if (!value) return onChange?.(undefined);

        if (multipleChoice) {
          if (valueFormat === ValueFormat.ObjectArray) {
            return onChange?.(value.map((val) => list?.find((item) => item._id === val)));
          }
          if (valueFormat === ValueFormat.ValueArray) {
            return onChange?.(value);
          }
          // 多选情况下，valueFormat 为 Object、Value 时，不合理，所以不处理
          return onChange?.(value);
        }

        if (valueFormat === ValueFormat.ObjectArray) {
          return onChange?.([list?.find((item) => item._id === value)]);
        }
        if (valueFormat === ValueFormat.ValueArray) {
          return onChange?.([value]);
        }
        if (valueFormat === ValueFormat.Object) {
          return onChange?.(list?.find((item) => item._id === value));
        }

        onChange?.(value);
      }}
    />
  );
};

export default ObjectItem;
