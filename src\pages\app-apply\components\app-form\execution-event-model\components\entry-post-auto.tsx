import { ENTRY_POST_PROPERTY } from '@/pages/app-designer/components/right-panel/form-design/button-options/constants';
import { QuestionCircleOutlined } from '@ant-design/icons';
import { Form, Input, Radio, Tooltip, TreeSelect } from 'antd';
import { useState } from 'react';

export type IProps = {
  isAuto?: boolean;
};

const EntryPostAuto = ({ isAuto }: IProps) => {
  const [deptOptions, setDeptOptions] = useState<any[]>([]);

  // 后端接口数据结构改变（集团部门树按公司分组），前端配合修改
  const getDeptListByOrg = (orgs) => {
    return orgs.map((org) => {
      Object.assign(org, {
        deptName: org.orgName,
        deptList: org.deptVOS,
        deptId: org.orgId,
        selectable: false,
        disabled: true,
      });
      return org;
    });
  };

  // const getDeptOptions = async () => {
  //   if (!state?.postId) return;
  //   const orgGroups = await org.getOrgAndSubDeptTree({
  //     postId: state?.postId || '',
  //     orgId: state?.orgId || '',
  //   });
  //   const deptList = getDeptListByOrg(orgGroups);
  //   setDeptOptions(deptList || []);
  // };

  // useEffect(() => {
  //   getDeptOptions();
  // }, [state?.postId]);

  return (
    <>
      {isAuto && (
        <>
          <Form.Item label="选择部门" name={'entryDeptId'} rules={[{ required: true, message: '请选择' }]}>
            <TreeSelect
              treeData={deptOptions}
              fieldNames={{ label: 'deptName', value: 'deptId', children: 'deptList' }}
              treeDefaultExpandAll
              allowClear
              showSearch
              style={{ width: '100%' }}
              placeholder="请选择"
              filterTreeNode={(inputValue, option) => {
                return option.deptName?.toLowerCase().indexOf(inputValue.toLowerCase()) >= 0;
              }}
            />
          </Form.Item>
          <Form.Item label="岗位名称" name={'entryPostName'} rules={[{ required: true, message: '请选择' }]}>
            <Input maxLength={20} placeholder="请输入" />
          </Form.Item>
        </>
      )}
      <Form.Item
        label="岗位类型"
        name={'entryPostType'}
        rules={[{ required: true, message: '请选择' }]}
        initialValue={ENTRY_POST_PROPERTY.PERSONNEL_POST}
      >
        <Radio.Group>
          <Radio value={ENTRY_POST_PROPERTY.PERSONNEL_POST}>人事岗</Radio>
          <Radio value={ENTRY_POST_PROPERTY.FULLTIME_POST}>
            全职岗
            <Tooltip title="人事岗+主职岗">
              <QuestionCircleOutlined style={{ marginLeft: 4 }} />
            </Tooltip>
          </Radio>
          <Radio value={ENTRY_POST_PROPERTY.DEPUTY_POST}>
            副职岗
            <Tooltip title="人事岗+兼职岗">
              <QuestionCircleOutlined style={{ marginLeft: 4 }} />
            </Tooltip>
          </Radio>
        </Radio.Group>
      </Form.Item>
    </>
  );
};

export default EntryPostAuto;
