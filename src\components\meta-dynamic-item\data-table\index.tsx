import { AppType } from '@/pages/app-designer/const';
import { datasourceDataAPI, systemDatametaAPI } from '@/services';
import { SourceType } from '@/types/form-field';
import eventEmitter, { EventName } from '@/utils/event-emitter';
import { getMetaTagUniqueId } from '@/utils/metadata';
import { PlusCircleOutlined } from '@ant-design/icons';
import { ActionType, EditableFormInstance, EditableProTable, ProColumns, RowEditableConfig, RowEditableType } from '@ant-design/pro-components';
import { Button, TablePaginationConfig, TableProps } from '@gwy/components-web';
import { isEmpty } from 'lodash-es';
import { forwardRef, Key, memo, useCallback, useEffect, useImperativeHandle, useMemo, useRef, useState } from 'react';
import { DataTableIOType } from './const';
import DynamicItem from './dynamic-item';
import ResizableHeader from './resizable-header';
import Summary from './summary';
import { getDynamicItemFormRules, handleTableData } from './utils';

export type DataTableProps = {
  // 是否是预览模式
  isPreview?: boolean;
  /**
   * 默认组件内自动调取后端接口获取数据反显
   * 当需要从外部赋值时，配置此方法，同时结合ref的setValues使用
   */
  getTableDatas?: () => Promise<any[]>;
  /**
   * 单行、多行编辑
   *
   * @default 'multiple'
   */
  editableType?: RowEditableType;
  /**
   * 滚动高度
   *
   * @default 400
   */
  scrollY?: number;
  setLoading: (loading: boolean) => void;
  tableConfig?: any;
  combineConfig?: any;
  formVersion?: any;
  needDefault?: boolean;
  dataId?: any;
  post?: any;
  cTagsMap?: any;
  bizId?: any;
};

export type DataTableRef = {
  getValues: () => Promise<any[]>;
  setValues: (vals: any[]) => void;
  reloadData: () => void;
  getDefaultData: (values) => void;
};

const pageSize = 20;

const DataTable = memo(
  forwardRef<DataTableRef, DataTableProps>(
    (
      {
        isPreview,
        scrollY = 400,
        setLoading,
        bizId,
        editableType = 'multiple',
        getTableDatas,
        cTagsMap,
        tableConfig,
        combineConfig,
        formVersion,
        needDefault,
        post,
        dataId,
      },
      ref,
    ) => {
      const [editableKeys, setEditableKeys] = useState<Key[]>([]);

      const [dataSource, setDataSource] = useState<any[]>([]);
      const [statisticList, setStatisticList] = useState<any[]>([]);

      const containerRef = useRef<HTMLDivElement>(null);
      const tableFormRef = useRef<EditableFormInstance>();
      const tableActionRef = useRef<ActionType>();

      const [currentPage, setCurrentPage] = useState(1);
      const [total, setTotal] = useState(0);
      const [defaultData, setDefaultData] = useState<any>({});

      const propsRef = useRef({
        getTableDatas,
      });
      propsRef.current.getTableDatas = getTableDatas;
      // const cTagsMap = {};

      // const configFields: any[] = [];
      const [configFields] = useMemo(() => {
        let cfgFields = [];
        if (!isEmpty(tableConfig)) {
          cfgFields = tableConfig?.fields?.map((field) => {
            const tag = cTagsMap[getMetaTagUniqueId(field)];
            return {
              ...field,
              ...tag,
            };
          });
        }
        if (!isEmpty(combineConfig)) {
          cfgFields = combineConfig?.fields?.map((field) => {
            const tag = cTagsMap[getMetaTagUniqueId(field)];
            return {
              ...field,
              ...tag,
            };
          });
        }
        return [cfgFields];
      }, [combineConfig, tableConfig, cTagsMap]);

      const tableIOType = configFields.some((t) => t.readonly)
        ? configFields.some((t) => !t.readonly)
          ? DataTableIOType.BOTH
          : DataTableIOType.OUTPUT
        : DataTableIOType.INPUT;

      const components = useMemo<TableProps['components']>(
        () => ({
          header: {
            cell: ResizableHeader,
          },
        }),
        [],
      );
      // 数据转换回显
      const transformData = (records, tags) => {
        const handleRecords = records.map((record) => {
          const values = { ...record };
          tags?.forEach((field) => {
            if (field.sourceType === SourceType.Field) {
              // console.log(record[getMetaTagUniqueId(field)], getMetaTagUniqueId(field), record,'field-----------')
              // debugger;
              return (values[getMetaTagUniqueId(field)] = record[getMetaTagUniqueId(field)]);
            }
            // const record = records.find((item) => item.dataUnitId === field.dataUnitId)?.record;
            // console.log(record, field, record[field.code], 'record---------');
            values[getMetaTagUniqueId(field)] = record[field.code];
          });
          return values;
        });
        return handleRecords;
      };

      const [tableCellWidthMap, setTableCellWidthMap] = useState<Record<string, number>>({});
      const columns = useMemo<ProColumns[]>(() => {
        console.log(cTagsMap, configFields, 'configFields-------');
        const cols = configFields.map<ProColumns>((field) => {
          const mainDataUnit = tableConfig?.dataUnits?.find((dataUnit) => dataUnit?.mainDataUnit) || formVersion?.dataUnits?.[0];
          const metaTagUniqueId = getMetaTagUniqueId(field);
          const mapKey = getMetaTagUniqueId(field);
          const colWidth = tableCellWidthMap[metaTagUniqueId] || 200;
          const curTag = cTagsMap[mapKey] || {};
          // console.log(curTag, metaTagUniqueId, 'curTag------')
          const col: ProColumns = {
            dataIndex: metaTagUniqueId,
            title: field?.fieldConfig?.fieldName ? (
              <span>
                {field?.fieldConfig?.fieldName}
                {/* <Tooltip title={`原标签名称：${curTag.name}`}>
                  <InfoCircleOutlined style={{ marginLeft: 5, color: '#4D7BF6' }} />
                </Tooltip> */}
              </span>
            ) : (
              curTag.name
            ),
            width: colWidth,
            editable: () => !field.readonly && field.readonly !== null,
            formItemProps: {
              rules: !field.readonly ? getDynamicItemFormRules(curTag, field, (value) => value?.data) : undefined,
              style: {
                marginBlock: 0,
              },
            },
            render: (val) => {
              if (field.sourceType === SourceType.Field) {
                return <span>{val || '_'}</span>;
              }
              return <DynamicItem tag={curTag} field={field} isPreview={isPreview} value={val} />;
            },
            renderFormItem: () => <DynamicItem tag={curTag} field={field} isPreview={isPreview} />,
            onHeaderCell: () =>
              ({
                width: colWidth,
                onResize: (e, { size }) => {
                  setTableCellWidthMap((pre) => ({
                    ...pre,
                    [metaTagUniqueId]: size?.width || 200,
                  }));
                },
              } as any),
          };
          return col;
        });
        cols.unshift({
          title: '序号',
          width: 85,
          editable: false,
          render: (v, r, k) => k + 1,
        });
        if (tableIOType === DataTableIOType.INPUT || editableType === 'single') {
          cols.push({
            title: '操作',
            valueType: 'option',
            width: 100,
            render: (v, r, k) => {
              return [
                editableType === 'single' ? (
                  <a key="edit" onClick={() => setEditableKeys([r.$$id])}>
                    编辑
                  </a>
                ) : (
                  false
                ),
                tableIOType === DataTableIOType.INPUT ? (
                  <a
                    key="delete"
                    onClick={() => {
                      setEditableKeys((pre) => pre.filter((key) => key !== r.$$id));
                      setDataSource((pre) => pre.filter((item) => item.$$id !== r.$$id));
                      setTotal((prev) => prev - 1);
                    }}
                  >
                    删除
                  </a>
                ) : (
                  false
                ),
              ].filter(Boolean);
            },
          });
        }

        return cols;
      }, [tableIOType, tableCellWidthMap, editableType, isPreview, configFields, cTagsMap]);

      const scroll = useMemo(() => ({ x: columns.reduce((width, col) => width + ((col.width as number) || 0), 0), y: scrollY }), [columns, scrollY]);

      const editable = useMemo<RowEditableConfig<any>>(
        () => ({
          // 单行编辑时提示文案
          onlyOneLineEditorAlertMessage: '只能同时编辑一行，请先保存当前编辑行',
          type: editableType,
          editableKeys: editableKeys,
          actionRender: (row, config, dom) =>
            [
              editableType === 'single' ? dom.save : false,
              tableIOType === DataTableIOType.INPUT ? (
                <a
                  key="delete"
                  onClick={() => {
                    setEditableKeys((pre) => pre.filter((key) => key !== row.$$id));
                    setDataSource((pre) => pre.filter((item) => item.$$id !== row.$$id));
                  }}
                >
                  删除
                </a>
              ) : (
                false
              ),
            ].filter(Boolean),
          onValuesChange(record, dataSource) {
            setDataSource(dataSource);
          },
        }),
        [editableKeys, tableIOType, editableType],
      );

      // 获取表格数据
      const getTableData = useCallback(async () => {
        try {
          setLoading(true);
          // TODO 判断条件
          // const flag = true
          const defaultParams = {
            formVersionId: formVersion?.formVersionId,
            postId: post?.postId,
            tableId: tableConfig?.id,
          };
          const DataUnitMain = tableConfig?.dataUnits?.find((dataUnit) => dataUnit?.mainDataUnit) || formVersion?.dataUnits?.[0] || {};
          const queryParams = {
            formVersionId: formVersion?.formVersionId,
            postId: post?.postId,
            pageSize,
            currentPage,
            queryConfig: {
              dataUnits: tableConfig?.dataUnits,
              queryFormType: 2,
              tableConFig: {
                dataId,
                tableId: tableConfig?.id,
                dataUnitId: DataUnitMain?.dataUnitId, // 来源行数据所属的数据单元id
              },
            },
          };
          // const returnParams = {}
          let renderData = null;
          let renderDefaultData = {};
          if (bizId) {
            const data = await systemDatametaAPI.getOrgDataUnitApproveData(bizId, tableConfig?.id);
            const { draftItems } = data || {};
            renderData = draftItems?.map((item) => item?.record) || [];
            const list = handleTableData(renderData, configFields, renderDefaultData);
            const transformLists = transformData(list, configFields);
            // console.log(transformLists, renderDefaultData, list, 'list---------');
            setDataSource(transformLists);
            setTotal(transformLists?.length || 0);
            // setStatisticList(renderData?.statisticList || [])
            if (editableType === 'multiple') {
              setEditableKeys(list.map((item) => item.$$id));
            }
          } else {
            // [renderData, renderDefaultData] = await Promise.all(
            //   needDefault
            //     ? [
            //         datasourceDataAPI.queryMutData(queryParams),
            //         // 获取默认数据
            //         datasourceDataAPI.getDefaultValue(defaultParams),
            //       ]
            //     : [datasourceDataAPI.queryMutData(queryParams)],
            // );
            if (needDefault) {
              if (formVersion?.appType !== AppType.SUBMIT) {
                renderData = await datasourceDataAPI.queryMutData(queryParams);
              }
              renderDefaultData = await datasourceDataAPI.getDefaultValue(defaultParams);
            } else {
              renderData = await datasourceDataAPI.queryMutData(queryParams);
            }
            const { records, statisticList, total } = renderData || {};
            setTotal(total);
            const list = handleTableData(records || [], configFields, renderDefaultData);
            const transformLists = transformData(list, configFields);
            setStatisticList(statisticList);
            setDefaultData(renderDefaultData);
            // console.log(transformLists, list,'list---------');
            setDataSource(transformLists);
            if (editableType === 'multiple') {
              setEditableKeys(list.map((item) => item.$$id));
            }
            // renderData = datas
          }
        } catch (e) {}
        setLoading(false);
      }, [editableType, setLoading, formVersion, dataId, tableConfig, post, formVersion, currentPage]);

      useEffect(() => {
        if (formVersion?.formVersionId && !isEmpty(tableConfig)) {
          typeof propsRef.current.getTableDatas !== 'function' && getTableData();
        }
      }, [formVersion]);

      useEffect(() => {
        if (formVersion?.formVersionId && !isEmpty(tableConfig) && !bizId && dataId) {
          typeof propsRef.current.getTableDatas !== 'function' && getTableData();
        }
      }, [currentPage, bizId]);

      useEffect(() => {
        const listener = (params) => {
          const { dataUnitId, tagId, value } = params || {};
          // TODO 判断条件中是否有动态入参关联了此标签
          if (typeof propsRef.current.getTableDatas === 'function') {
            propsRef.current.getTableDatas();
          } else {
            // getTableData();
          }
        };

        eventEmitter.on(EventName.dynamicParamChange, listener);

        return () => {
          eventEmitter.off(EventName.dynamicParamChange, listener);
        };
      }, [getTableData]);

      // 统计项
      const summary = useCallback(
        () => (tableConfig?.statisticList?.length > 0 ? <Summary columns={configFields} statisticalItems={statisticList || []} /> : null),
        [tableConfig, statisticList],
      );

      const pagination: TablePaginationConfig = useMemo(
        () => ({
          pageSize,
          current: currentPage,
          showSizeChanger: false,
          size: 'small',
          total: total,
          showTotal: (total) => `总共 ${total} 条`,
          onChange: (page) => setCurrentPage(page),
        }),
        [currentPage, total],
      );

      useImperativeHandle(ref, () => ({
        reloadData: getTableData,
        setValues: (values) => {
          if (typeof getTableDatas === 'function') {
            console.log(values, 'values-----!');
            setDataSource(values);
          }
        },
        getValues: async () => {
          console.log(dataSource, 'dataSource-------------');
          await tableFormRef.current?.validateFields();
          return Object.values(dataSource).map((item) => {
            return (
              Object.keys(item)
                // .filter((key) => {
                //   const { readonly } = configFields.find((field) => getMetaTagUniqueId(field) === key) || {};
                //   return !readonly;
                // })
                .reduce((obj, key) => {
                  // $$开头代表页面自定义字段，无需传给后端
                  if (key.startsWith('$$')) {
                    return obj;
                  }
                  // 接口返的非标签数据
                  if (['_id'].includes(key)) {
                    obj[key] = item[key];
                  } else {
                    obj[key] = item[key]?.data;
                  }
                  return obj;
                }, {})
            );
          });
        },
        getDefaultData: (values) => {
          setDefaultData(values);
        },
      }));

      return (
        <div ref={containerRef}>
          <EditableProTable
            rowKey="$$id"
            actionRef={tableActionRef}
            editableFormRef={tableFormRef}
            columns={columns}
            value={dataSource}
            editable={editable}
            recordCreatorProps={false}
            size="small"
            components={components}
            scroll={scroll}
            summary={summary}
            pagination={pagination}
          />
          {tableIOType === DataTableIOType.INPUT && (
            <Button
              type="link"
              icon={<PlusCircleOutlined />}
              style={{ paddingLeft: 0, paddingRight: 0 }}
              onClick={() => {
                const $$id = new Date().getTime();
                if (editableType === 'multiple') {
                  setEditableKeys((pre) => [...pre, $$id]);
                }
                // 自动切换到最后一页
                if (currentPage !== Math.ceil((dataSource.length + 1) / pageSize)) {
                  setCurrentPage(Math.floor((dataSource.length + 1) / pageSize) + 1);
                  setTotal((prev) => prev + 1);
                }
                console.log(defaultData, 'defaultData-----');
                tableActionRef.current.addEditRecord(
                  { $$id, ...defaultData },
                  {
                    recordKey: '$$id',
                    newRecordType: 'dataSource',
                  },
                );
                // 新添加的一条滚到可视区
                setTimeout(() => {
                  if (containerRef.current) {
                    const lastElementList = containerRef.current.querySelectorAll(`.ant-table-tbody .ant-table-row`);
                    const lastElement = lastElementList[lastElementList.length - 1];
                    if (lastElement) {
                      lastElement.scrollIntoView({ behavior: 'smooth' });
                    }
                  }
                }, 100);
              }}
            >
              添加一行
            </Button>
          )}
        </div>
      );
    },
  ),
);

export default DataTable;
