import { DeleteOutlined, EditOutlined } from '@ant-design/icons';
import { Form, Radio, Select } from '@gwy/components-web';
import { useMemo } from 'react';
import styles from './index.less';

interface IProps {
  field?: any;
  index?: number;
  posts?: any[];
  setConditionGroup: () => void;
  onRemove: () => void;
}

const AuthorizeItem = ({ field, index, posts, setConditionGroup, onRemove }: IProps) => {
  const form = Form.useFormInstance();
  const postOptions = useMemo(
    () =>
      posts.map((post) => ({
        label: post.postName,
        value: post.postId,
      })),
    [posts],
  );
  const authorizePostConfs = form.getFieldValue(['authorizePostConfs']);

  return (
    <div className={styles.container}>
      <div className={styles.idx}>{index + 1}</div>
      <div className={styles.content}>
        <div className={styles.contentItem}>
          <Form.Item style={{ width: 300 }} labelAlign="left" wrapperCol={{ span: 16 }} label="授权给" name={[field.name, 'postId']}>
            <Select options={postOptions} />
          </Form.Item>
          <span className={styles.settingBtn} onClick={() => setConditionGroup()}>
            <EditOutlined /> 配置条件
          </span>
        </div>
        <div className={styles.contentItem}>
          <Form.Item
            style={{ width: 300 }}
            labelAlign="left"
            labelCol={{ span: 11 }}
            wrapperCol={{ span: 13 }}
            label="管理员岗是否复核"
            name={[field.name, 'review']}
          >
            <Radio.Group>
              <Radio value={false}>否</Radio>
              <Radio value={true}>是</Radio>
            </Radio.Group>
          </Form.Item>
        </div>
      </div>
      <div className={styles.right}>
        <DeleteOutlined style={{ fontSize: 16, cursor: 'pointer' }} onClick={onRemove} />
      </div>
    </div>
  );
};

export default AuthorizeItem;
